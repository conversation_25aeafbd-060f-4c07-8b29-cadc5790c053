@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

:: Xing Blog Windows 快速部署脚本
:: 使用方法: quick-deploy.bat [preview|deploy]

:: 配置变量
set SERVER_IP=************
set USERNAME=root
set REMOTE_PATH=/var/www/xing-blog
set LOCAL_PORT=4000

:: 颜色代码 (Windows 10+)
set "RED=[91m"
set "GREEN=[92m"
set "YELLOW=[93m"
set "BLUE=[94m"
set "PURPLE=[95m"
set "NC=[0m"

:: 显示横幅
echo.
echo %PURPLE%  ╔══════════════════════════════════════╗%NC%
echo %PURPLE%  ║           Xing Blog 部署工具          ║%NC%
echo %PURPLE%  ║        快速预览 ^& 一键部署            ║%NC%
echo %PURPLE%  ╚══════════════════════════════════════╝%NC%
echo.

:: 检查Node.js
echo %BLUE%🔍 检查环境依赖...%NC%
node --version >nul 2>&1
if errorlevel 1 (
    echo %RED%❌ Node.js 未安装，请先安装 Node.js%NC%
    pause
    exit /b 1
)

:: 检查是否在正确目录
if not exist "package.json" (
    echo %RED%❌ 请在博客根目录下运行此脚本%NC%
    pause
    exit /b 1
)

:: 安装依赖
if not exist "node_modules" (
    echo %BLUE%📦 安装项目依赖...%NC%
    npm install
)

echo %GREEN%✅ 环境检查通过%NC%

:: 解析命令行参数
if "%1"=="preview" goto :preview
if "%1"=="deploy" goto :deploy
if "%1"=="help" goto :help

:: 交互式菜单
:menu
echo.
echo %BLUE%请选择操作:%NC%
echo   %GREEN%1^)%NC% 本地预览
echo   %GREEN%2^)%NC% 生成并部署到服务器
echo   %GREEN%3^)%NC% 仅生成静态文件
echo   %GREEN%4^)%NC% 退出
echo.
set /p choice="请输入选项 (1-4): "

if "%choice%"=="1" goto :preview
if "%choice%"=="2" goto :deploy_confirm
if "%choice%"=="3" goto :generate_only
if "%choice%"=="4" goto :exit
echo %RED%❌ 无效选项，请重新选择%NC%
goto :menu

:: 本地预览
:preview
echo %BLUE%🔨 生成静态文件...%NC%
call hexo clean
call hexo generate

if not exist "public" (
    echo %RED%❌ 静态文件生成失败%NC%
    pause
    exit /b 1
)

echo %GREEN%✅ 静态文件生成完成%NC%
echo.
echo %BLUE%🚀 启动本地预览服务器...%NC%
echo %YELLOW%📝 预览地址: http://localhost:%LOCAL_PORT%%NC%
echo %YELLOW%💡 按 Ctrl+C 停止预览服务器%NC%
echo %YELLOW%🔄 文件修改后会自动刷新页面%NC%
echo.

:: 启动预览服务器
call hexo server -p %LOCAL_PORT%
goto :menu

:: 部署确认
:deploy_confirm
echo %BLUE%🔨 生成静态文件...%NC%
call hexo clean
call hexo generate

if not exist "public" (
    echo %RED%❌ 静态文件生成失败%NC%
    pause
    exit /b 1
)

echo %GREEN%✅ 静态文件生成完成%NC%

:: 显示文件统计
for /f %%i in ('dir /s /b public\* ^| find /c /v ""') do set FILE_COUNT=%%i
echo %BLUE%📊 生成了 !FILE_COUNT! 个文件%NC%

echo.
echo %YELLOW%⚠️  即将部署到生产服务器！%NC%
set /p confirm="确认部署? (y/N): "
if /i not "%confirm%"=="y" (
    echo %YELLOW%⏹️  部署已取消%NC%
    goto :menu
)

:: 直接部署
:deploy
echo %BLUE%🚀 开始部署到服务器...%NC%

:: 创建时间戳
for /f "tokens=2 delims==" %%a in ('wmic OS Get localdatetime /value') do set "dt=%%a"
set "TIMESTAMP=%dt:~0,4%%dt:~4,2%%dt:~6,2%-%dt:~8,2%%dt:~10,2%%dt:~12,2%"
set "PACKAGE_NAME=xing-blog-%TIMESTAMP%.tar.gz"

:: 检查tar命令
tar --version >nul 2>&1
if errorlevel 1 (
    echo %RED%❌ 系统缺少 tar 命令，请使用 Git Bash 或 WSL 运行 quick-deploy.sh%NC%
    echo %YELLOW%💡 或者手动上传 public 目录内容到服务器%NC%
    pause
    exit /b 1
)

:: 打包文件
echo %BLUE%📦 打包静态文件...%NC%
cd public
tar -czf "../%PACKAGE_NAME%" *
cd ..

:: 上传和部署 (需要配置SSH密钥)
echo %BLUE%📤 上传文件到服务器...%NC%
scp "%PACKAGE_NAME%" %USERNAME%@%SERVER_IP%:/tmp/

if errorlevel 1 (
    echo %RED%❌ 文件上传失败，请检查SSH配置%NC%
    del "%PACKAGE_NAME%"
    pause
    exit /b 1
)

:: 在服务器上部署
echo %BLUE%🔧 在服务器上部署...%NC%
ssh %USERNAME%@%SERVER_IP% "cd /tmp && sudo mkdir -p %REMOTE_PATH% && sudo rm -rf %REMOTE_PATH%/* && sudo tar -xzf %PACKAGE_NAME% -C %REMOTE_PATH% && sudo chown -R www-data:www-data %REMOTE_PATH% && sudo chmod -R 755 %REMOTE_PATH% && sudo nginx -t && sudo systemctl reload nginx && rm -f %PACKAGE_NAME%"

if errorlevel 1 (
    echo %RED%❌ 服务器部署失败%NC%
    del "%PACKAGE_NAME%"
    pause
    exit /b 1
)

:: 清理本地文件
del "%PACKAGE_NAME%"

echo %GREEN%🎉 部署完成！%NC%
echo %BLUE%🌐 访问地址: https://an.xing2006.me%NC%
goto :menu

:: 仅生成静态文件
:generate_only
echo %BLUE%🔨 生成静态文件...%NC%
call hexo clean
call hexo generate

if not exist "public" (
    echo %RED%❌ 静态文件生成失败%NC%
    pause
    exit /b 1
)

echo %GREEN%✅ 静态文件已生成到 public\ 目录%NC%
goto :menu

:: 显示帮助
:help
echo %BLUE%使用方法:%NC%
echo   quick-deploy.bat [选项]
echo.
echo %BLUE%选项:%NC%
echo   preview    仅启动本地预览，不部署
echo   deploy     直接部署，跳过预览
echo   help       显示此帮助信息
echo.
echo %BLUE%示例:%NC%
echo   quick-deploy.bat           # 交互式模式
echo   quick-deploy.bat preview   # 仅预览
echo   quick-deploy.bat deploy    # 直接部署
pause
exit /b 0

:: 退出
:exit
echo %BLUE%👋 再见！%NC%
pause
exit /b 0

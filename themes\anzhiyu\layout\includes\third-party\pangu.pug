script.
  function panguFn () {
    if (typeof pangu === 'object') pangu.autoSpacingPage()
    else {
      getScript('!{url_for(theme.asset.pangu)}')
        .then(() => {
          pangu.autoSpacingPage()
        })
    }
  }

  function panguInit () {
    if (!{theme.pangu.field === 'post'}){
      GLOBAL_CONFIG_SITE.isPost && panguFn()
    } else {
      panguFn()
    }
  }

  document.addEventListener('DOMContentLoaded', panguInit)

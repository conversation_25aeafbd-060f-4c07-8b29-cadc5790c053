#!/bin/bash

# AnZhiYu博客自动部署脚本
# 使用方法: ./deploy.sh [服务器IP] [用户名] [博客路径]

set -e  # 遇到错误立即退出

# 配置参数
SERVER_IP=${1:-"your-server-ip"}
USERNAME=${2:-"your-username"}
BLOG_PATH=${3:-"/var/www/anzhiyu-blog"}
BACKUP_PATH="/var/backups/blog-backup-$(date +%Y%m%d-%H%M%S)"

echo "🚀 开始部署 AnZhiYu 博客到服务器..."
echo "服务器: $USERNAME@$SERVER_IP"
echo "路径: $BLOG_PATH"
echo ""

# 1. 检查本地git状态
echo "📋 检查本地git状态..."
if [ -n "$(git status --porcelain)" ]; then
    echo "⚠️  警告: 本地有未提交的更改"
    git status --short
    read -p "是否继续部署? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "❌ 部署已取消"
        exit 1
    fi
fi

# 2. 推送到远程仓库（如果有）
echo "📤 推送到远程仓库..."
git push origin master 2>/dev/null || echo "⚠️  无法推送到远程仓库，继续部署..."

# 3. 连接服务器并部署
echo "🔗 连接服务器并开始部署..."

ssh $USERNAME@$SERVER_IP << EOF
    set -e
    
    echo "📍 当前在服务器上操作..."
    
    # 检查博客目录是否存在
    if [ ! -d "$BLOG_PATH" ]; then
        echo "❌ 博客目录不存在: $BLOG_PATH"
        exit 1
    fi
    
    cd $BLOG_PATH
    
    # 停止服务
    echo "⏹️  停止当前服务..."
    pm2 stop hexo-blog 2>/dev/null || pkill -f "hexo server" 2>/dev/null || echo "没有运行的服务"
    
    # 备份当前版本
    echo "💾 备份当前版本到 $BACKUP_PATH..."
    mkdir -p $(dirname $BACKUP_PATH)
    cp -r $BLOG_PATH $BACKUP_PATH
    
    # 拉取最新代码
    echo "📥 拉取最新代码..."
    git pull origin master
    
    # 安装依赖
    echo "📦 安装/更新依赖..."
    npm install --production
    
    # 清理并重新生成
    echo "🧹 清理缓存..."
    npx hexo clean
    
    echo "🔨 生成静态文件..."
    npx hexo generate
    
    # 重启服务
    echo "🔄 重启服务..."
    pm2 start ecosystem.config.js 2>/dev/null || pm2 start hexo-blog 2>/dev/null || {
        echo "🚀 启动 Hexo 服务器..."
        nohup npx hexo server > hexo.log 2>&1 &
        echo \$! > hexo.pid
    }
    
    echo "✅ 部署完成!"
    echo "📊 服务状态:"
    pm2 status 2>/dev/null || ps aux | grep hexo | grep -v grep || echo "服务可能未正常启动"
    
EOF

echo ""
echo "🎉 部署完成!"
echo "📝 部署日志已保存"
echo "🌐 请访问您的网站检查更新效果"
echo ""
echo "💡 如果遇到问题，可以使用以下命令回滚:"
echo "   ssh $USERNAME@$SERVER_IP 'cp -r $BACKUP_PATH/* $BLOG_PATH/ && cd $BLOG_PATH && npm install && hexo clean && hexo generate && pm2 restart hexo-blog'"

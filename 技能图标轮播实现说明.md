# 技能图标轮播实现说明

## 功能概述

成功为安知鱼主题首页添加了技能图标轮播效果，在"生活明朗，万物可爱"标题下方显示各种技术栈图标的无缝滚动轮播。

## 实现效果

- ✅ 在首页标题区域显示技能图标轮播
- ✅ 包含 20 种主流技术栈图标（HTML5、CSS3、JavaScript、TypeScript、Vue.js、React、Node.js、Python、Java、Git、Docker、MySQL、MongoDB、Redis、Nginx、Linux、VS Code、Photoshop、Figma、Webpack）
- ✅ 每个图标都有对应的品牌色彩背景
- ✅ 30秒无缝循环滚动动画
- ✅ 图标悬停时有缩放效果
- ✅ 响应式设计，适配不同屏幕尺寸

## 修改的文件

### 1. 数据配置文件
**文件**: `source/_data/creativity.yml`
- 添加了技能图标配置部分
- 包含 20 个技术栈图标的名称、图标链接和品牌色彩
- 保留了原有的推荐文章配置

### 2. 模板文件
**文件**: `themes/anzhiyu/layout/includes/anzhiyu/tags-group-all.pug`
- 修改模板逻辑，只显示 `class_name` 为 "技能" 的图标
- 实现图标的成对排列和重复循环
- 优化了模板结构

### 3. 样式文件
**文件**: `themes/anzhiyu/source/css/_extra/skills/skills.css`
- 调整图标容器大小为 80x80px
- 图标图片大小为 40x40px
- 添加悬停缩放效果
- 设置 30秒循环动画
- 优化布局和间距

## 技术特点

1. **无缝循环**: 通过重复渲染图标实现无缝滚动效果
2. **品牌色彩**: 每个图标使用对应技术的官方品牌色
3. **高质量图标**: 使用 DevIcons CDN 提供的 SVG 图标
4. **性能优化**: 使用 CSS 动画，性能流畅
5. **响应式设计**: 适配不同设备屏幕

## 图标列表

| 技术栈 | 颜色 | 说明 |
|--------|------|------|
| HTML5 | #E34F26 | 网页标记语言 |
| CSS3 | #1572B6 | 样式表语言 |
| JavaScript | #F7DF1E | 编程语言 |
| TypeScript | #3178C6 | JavaScript 超集 |
| Vue.js | #4FC08D | 前端框架 |
| React | #61DAFB | 前端框架 |
| Node.js | #339933 | 后端运行时 |
| Python | #3776AB | 编程语言 |
| Java | #ED8B00 | 编程语言 |
| Git | #F05032 | 版本控制 |
| Docker | #2496ED | 容器化平台 |
| MySQL | #4479A1 | 关系型数据库 |
| MongoDB | #47A248 | 文档数据库 |
| Redis | #DC382D | 内存数据库 |
| Nginx | #009639 | Web 服务器 |
| Linux | #FCC624 | 操作系统 |
| VS Code | #007ACC | 代码编辑器 |
| Photoshop | #31A8FF | 图像编辑 |
| Figma | #F24E1E | 设计工具 |
| Webpack | #8DD6F9 | 构建工具 |

## 使用方法

1. 确保 `source/_data/creativity.yml` 文件包含技能配置
2. 重新生成博客: `hexo clean && hexo generate`
3. 启动服务器: `hexo server`
4. 访问首页即可看到轮播效果

## 自定义说明

如需添加或修改图标：

1. 编辑 `source/_data/creativity.yml` 文件
2. 在 "技能" 分类下添加新的图标配置
3. 格式如下：
```yaml
- name: 技术名称
  icon: 图标链接
  color: "#品牌色彩"
```

## 注意事项

- 图标使用 DevIcons CDN，确保网络连接正常
- 建议图标数量保持在 15-25 个之间，以获得最佳视觉效果
- 可以通过修改 CSS 中的动画时间来调整滚动速度

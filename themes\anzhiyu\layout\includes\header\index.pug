if !theme.disable_top_img && page.top_img !== false
  if is_post()
    - var top_img = page.top_img || page.cover || page.randomcover
  else if is_page()
    - var top_img = page.top_img || theme.default_top_img
  else if is_home()
    - var top_img = theme.index_img !== false ? theme.index_img || theme.default_top_img : false
    - var home_index_img = theme.index_img !== false ? theme.index_img || theme.default_top_img : false
  else
    - var top_img = page.top_img || theme.default_top_img

  if top_img !== false
    - var imgSource = top_img && top_img.indexOf('/') !== -1 ? url_for(top_img) : top_img
    - var bg_img = top_img ? imgSource : ''
    - var home_index_img_bg = home_index_img ? home_index_img : ''
    - var site_title = page.title || page.tag || page.category || config.title
    - var isHomeClass = is_home() ? 'full_page' : 'not-home-page'
    - is_post() ? isHomeClass = 'post-bg' : isHomeClass
  else
    - var isHomeClass = 'not-top-img'
else
  - var top_img = false
  - var isHomeClass = 'not-top-img'
header#page-header(class=`${isHomeClass}` style=home_index_img_bg)
  !=partial('includes/header/nav', {}, {cache: true})
  if top_img !== false
    if is_post()
      if page.bilibili_bg
        !=partial('includes/bili-banner/index')
      else
        include ./post-info.pug
        if theme.dynamicEffect && theme.dynamicEffect.postTopWave
          section.main-hero-waves-area.waves-area
            svg.waves-svg(xmlns='http://www.w3.org/2000/svg', xlink='http://www.w3.org/1999/xlink', viewBox='0 24 150 28', preserveAspectRatio='none', shape-rendering='auto')
              defs
                path#gentle-wave(d='M -160 44 c 30 0 58 -18 88 -18 s 58 18 88 18 s 58 -18 88 -18 s 58 18 88 18 v 44 h -352 Z')
              g.parallax
                use(href='#gentle-wave', x='48', y='0')
                use(href='#gentle-wave', x='48', y='3')
                use(href='#gentle-wave', x='48', y='5')
                use(href='#gentle-wave', x='48', y='7')
        #post-top-cover
          img#post-top-bg(class='nolazyload' src=bg_img)

    else if is_home() 
      #site-info
        h1#site-title=site_title
        if theme.subtitle.enable
          - var loadSubJs = true
          #site-subtitle
            span#subtitle
        if(theme.social)
          #site_social_icons
            !=fragment_cache('social', function(){return partial('includes/header/social')})
      #scroll-down
        i.anzhiyufont.anzhiyu-icon-angle-down.scroll-down-effects
    else
      #page-site-info
        h1#site-title=site_title

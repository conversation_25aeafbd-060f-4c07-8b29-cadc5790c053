#pagination
  .toPageGroup
    display: flex;
    position: relative;
    margin: 0;
    box-shadow: none;
    width: auto;
    &:hover 
      background: none;
  .pagination
    text-align: center
    .extend.next 
      .pagination_tips_next
        margin-left: -32px;
        transition: 0.3s;
        opacity: 0;

  .page-number
    &.current
      background: $theme-paginator-color
      color: var(--white)

  .pagination-info
    position: absolute
    if hexo-config("post_pagination") == '3' && hexo-config("post_pagination") == '4'
      top: auto
    else
      top: 50%
    padding: 20px 40px
    width: 100%
    transform: translate(0, -50%)

  .prev_info,
  .next_info
    @extend .limit-one-line
    color: var(--white)
    font-weight: 500

  .next-post
    .pagination-info
      text-align: right

  .pull-full
    width: 100% !important

  .prev-post .label,
  .next-post .label
    color: var(--light-grey)
    text-transform: uppercase
    font-size: 90%

  .prev-post,
  .next-post
    @extend .postImgHover
    width: 50%

    +maxWidth768()
      width: 100%

    a
      position: relative
      display: block
      overflow: hidden
      height: 150px

  &.pagination-post
    overflow: hidden
    margin-top: 1.25rem
    width: 100%
    background: $dark-black
    border-radius: 10px;
    if hexo-config("post_pagination") == '3' || hexo-config("post_pagination") == '4'
      +minWidth1200()
        position: fixed;
        width: 300px;
        bottom: -100px;
        right: 60px;
        z-index: 1000;
        height: fit-content;
        transition: cubic-bezier(.42,0,.3,1.11) .3s;
        border: var(--style-border);
        border-radius: 12px;
        overflow: hidden;
        cursor: pointer;
        opacity: 0;
        z-index: 1002;
        background: 0 0;
        border-radius: 10px;
        .prev-post.pull-left
          display: none
        &:hover
          border: var(--style-border-hover);
          .next-post > a,
          .prev-post > a
            background: var(--anzhiyu-main);
            .pagination-info
              .label,
              .next_info,
              .prev_info
                color: var(--anzhiyu-white)
        &.show-window
          bottom: 20px;
          opacity: 1;
        .prev-post.pull-left,
        .prev-post.pull-full,
        .next-post.pull-right,
        .next-post.pull-full
          background: var(--anzhiyu-maskbgdeep);
          backdrop-filter: blur(5px);
          transform: translateZ(0);
          width: 100%;
          > a
            border: none;
            height: fit-content;
            padding: 0.5rem 0;
          img 
            if hexo-config("post_pagination") == '3'
              display: none
            top: 0;
          .pagination-info
            text-align: left;
            position: relative;
            padding: 0.5rem 1rem;
            transform: none;
            display: flex;
            top: 0;
            flex-direction: column;
            justify-content: center;
            margin: auto;
            height: 100%;
            .label
              color: var(--anzhiyu-fontcolor);
              font-weight: 700;
              font-size: 12px;
              margin-bottom: 0.5rem;
              border-bottom: var(--style-border);
              line-height: 1;
              padding-bottom: 0.5rem;
            .next_info,
            .prev_info
              color: var(--anzhiyu-fontcolor);
              -webkit-line-clamp: 2;
              white-space: normal;
              line-height: 1.3;
              display: -webkit-box;
              -webkit-box-orient: vertical;
              overflow: hidden;
              font-size: 14px;
              font-weight: 400;
              margin-bottom: 0;
    
  +maxWidth768()
    .toPageGroup
      display: none
    .page-number
      display: none !important
    .space
      display: none !important
    .pagination 
      .extend.next 
        width: 100%;
        height: 3rem
        margin: 0;
        border-radius: 12px;
        line-height: 3rem
        background: var(--anzhiyu-card-bg);
        border: var(--style-border-always);
        box-shadow: var(--anzhiyu-shadow-border);
        margin: 0 20px
        &:hover
          background: var(--anzhiyu-theme);
          color: var(--anzhiyu-white);
        i
          display: none
        .pagination_tips_next
          opacity: 1;
          margin-left: 0;
        
      .extend.prev
        width: 100%;
        height: 3rem;
        border-radius: 12px;
        line-height: 3rem;
        background: var(--anzhiyu-card-bg);
        border: var(--style-border-always);
        box-shadow: var(--anzhiyu-shadow-border);
        margin: 0 20px
        i
          display: none
        .pagination_tips_prev
          opacity: 1;
          margin-right: 0
.layout
  & > .recent-posts
    .pagination
      & > *:not(pangu)
        display: inline-block
        width: w = 2.5em
        height: w
        line-height: w

      & > *:not(.space):not(pangu)
        @extend .cardHover
        transition: 0s;

  & > div:not(.recent-posts)
    .pagination
      .page-number
        display: inline-block
        margin: 0 4px
        min-width: w = 2.5em
        height: w
        text-align: center
        line-height: w
        cursor: pointer

// 分页样式
@media screen and (min-width: 1200px)
  #pagination a.extend.next:hover,
  #pagination a.extend.prev:hover
    transform: scale(1.03)

+minWidth768()
  #pagination .page-number.current:hover
    background: var(--anzhiyu-theme)
    box-shadow: var(--anzhiyu-shadow-theme)
    color: var(--anzhiyu-white)

  #pagination a.extend.next:hover,
  #pagination a.extend.prev:hover
    color: var(--anzhiyu-theme)
    border: var(--style-border-hover)
    transform: scale(1.03)

  nav#pagination
    overflow: visible

  #pagination .page-number.current
    background: var(--anzhiyu-theme)
    border: var(--style-border-hover)
    box-shadow: var(--anzhiyu-shadow-theme)

  #pagination a.extend.next
    overflow: hidden
    height: 2.5em
    line-height: 2.5em

  #pagination a.extend.next,
  #pagination a.extend.prev
    width: 5rem !important
    line-height: 1.9rem !important
    border-radius: 8px !important
    background: var(--anzhiyu-card-bg)
    box-shadow: var(--anzhiyu-shadow-border)
    border: var(--style-border)
    display: flex !important
    align-items: center
    justify-content: center
    overflow: hidden
    transition: all 0.3s, color 0s
    position: absolute

  #pagination .page-number
    background: var(--anzhiyu-card-bg)
    border-radius: 8px !important
    margin: 0 0.3rem
    box-shadow: var(--anzhiyu-shadow-border)
    border: var(--style-border)
    transition: 0.3s

  #pagination .page-number:hover
    color: var(--anzhiyu-theme)
    border: var(--style-border-hover)
    box-shadow: var(--anzhiyu-shadow-main)

#pagination .pagination
  position: relative
  display: flex
  justify-content: center

#pagination a.extend.next:hover .pagination_tips_next
  margin-left: 2px
  opacity: 1
  white-space: nowrap

#pagination a.extend.prev .pagination_tips_prev
  margin-right: -32px
  transition: 0.3s
  opacity: 0

#pagination a.extend.prev:hover .pagination_tips_prev
  margin-right: 2px
  opacity: 1
  white-space: nowrap

a.extend.prev
  left: 0

a.extend.next
  right: 0

#pagination
  overflow: hidden
  margin-top: 1rem
  width: 100%

+maxWidth768()
  #site-name
    padding: 0 !important

#pagination .pagination input
  width: 2.5em
  height: 2.5em
  border-radius: 8px
  border: var(--style-border-always)
  transition: 0.3s
  outline-style: none
  font-size: 16px
  padding-left: 12px

a#toPageButton
  display: flex
  position: absolute
  width: 2.5em
  height: 2.5em
  right: 0px
  top: 0px
  border-radius: 8px
  justify-content: center
  align-items: center
  transition: 0.3s
  background: var(--anzhiyu-card-bg)
  border: var(--style-border-always)
  cursor: pointer

#pagination .pagination .toPageGroup:hover input,
#pagination .pagination .toPageGroup input:focus
  border: var(--style-border-hover-always)
  outline-style: none
  width: 100px

#pagination .toPageGroup:hover a#toPageButton,
#pagination .toPageGroup:focus-within a#toPageButton
  width: 30px
  height: 30px
  right: 4px
  top: 5px
  background: var(--anzhiyu-secondbg)
  border: 1px solid var(--anzhiyu-none)
  border-radius: 4px

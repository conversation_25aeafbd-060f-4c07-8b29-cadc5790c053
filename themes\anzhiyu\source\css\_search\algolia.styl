#algolia-search
  .search-dialog
    .ais-SearchBox
      input
        padding: .5rem 1rem
        width: 100%
        outline: none
        border: var(--style-border)
        border-radius: var(--anzhiyu-radius)
        background: var(--anzhiyu-secondbg)
        color: var(--search-input-color)
        &:focus
          border: var(--style-border-hover)
    .search-dialog-title
      font-weight: 700;
      color: var(--anzhiyu-main);


    .ais-Hits-list
      margin: 0
      padding: 0
      @extend .list-beauty

      .ais-Hits-item
        &:hover
          background: var(--anzhiyu-main)
          cursor pointer
          transition: all .2s;
          a
            color: var(--anzhiyu-white)
      mark
        background: transparent
        color: $search-keyword-highlight
        font-weight: bold

    .algolia-hit-item-content
      margin: 0 0 8px
      word-break: break-all

    .ais-Pagination
      margin: 20px 0 0
      padding: 0
      text-align: center

      .ais-Pagination-list
        margin: 0
        padding: 0
        list-style: none

      .ais-Pagination-item
        display: inline
        margin: 0 4px
        padding: 0

        .ais-Pagination-link
          display: inline-block
          min-width: 24px
          height: 24px
          text-align: center
          line-height: 24px

      .ais-Pagination-item--selected
        a
          background: $theme-paginator-color
          color: #eee
          cursor: default

      .ais-Pagination-item--disabled
        visibility: hidden

    .algolia-logo
      padding-top: 2px
      width: 80px
      height: 30px

    #algolia-hits
      overflow-y: scroll
      min-height: 20px
      .tag-list
        padding: 4px 8px;
        border-radius: 8px;
        margin-right: 0.5rem;
        margin-top: 0.5rem;
        border: var(--style-border-always);
        cursor: pointer;

    #algolia-info
      div
        display: inline

      .algolia-poweredBy
        float: right
#algolia-search #search-mask
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  background: var(--anzhiyu-maskbg);

#algolia-search .search-dialog
  border-radius: 12px;
  box-shadow: var(--anzhiyu-shadow-lightblack);
  background: var(--anzhiyu-card-bg);
  border: var(--style-border);
  transition: 0.3s;

#algolia-search .search-dialog .ais-Pagination .ais-Pagination-item--selected a
  border-radius: 4px;
  background: var(--anzhiyu-main);
  color: var(--anzhiyu-white);

#algolia-search .search-dialog:hover
  border: var(--style-border-hover);
  box-shadow: var(--anzhiyu-shadow-theme);
  +maxWidth768()
    border: var(--style-border)

#algolia-search .list-beauty li,
#algolia-search .category-lists ul li,
#algolia-search .search-dialog .ais-Hits-list li
  padding: 0;

#algolia-search .list-beauty li:before,
#algolia-search .category-lists ul li:before,
#algolia-search .search-dialog .ais-Hits-list li:before
  display: none;

#algolia-search .list-beauty li,
#algolia-search .category-lists ul li,
#algolia-search .search-dialog .ais-Hits-list li
  border-radius: 8px;
  border: var(--style-border);
  margin-bottom: 10px;
  display: flex;
  justify-content: space-between;
  align-items: center;

#algolia-search .search-dialog .ais-Hits-list a
  color: var(--search-result-title);
  display: block;
  font-weight: 600;
  padding: 5px 10px;
  width: 100%;
  transition: all .2s;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;

#algolia-search .search-dialog .ais-Hits-list .search-result-tags a
  padding: 4px 8px;
  border-radius: 8px;
  margin-right: 0.5rem;
  margin-top: 0.5rem;
  border: var(--style-border);
  display: inline;

#algolia-search .ais-SearchBox-loadingIndicator
  display: none;

# 关于页面配置说明

## 📋 配置完成项目

已成功配置了一个类似 https://blog.anheyu.com/about/ 的关于本人界面，包含以下功能：

### ✅ 已配置的功能

1. **基础信息配置**
   - 个人头像和基本信息
   - 技能标签展示
   - 个人描述和座右铭

2. **动态效果区域**
   - Hello About 鼠标跟随效果
   - 技能轮播展示
   - 追求理念动态文字

3. **个人信息展示**
   - 职业生涯时间线
   - 地图位置信息
   - 性格测试结果
   - 个人照片展示

4. **兴趣爱好**
   - 游戏信息（原神UID）
   - 番剧收藏列表
   - 音乐偏好
   - 数码科技关注

5. **统计信息**
   - 51LA访问统计（需配置）
   - 网站运行时间
   - 数据可视化展示

6. **赞赏功能**
   - 赞赏名单展示
   - 充电动画效果
   - 支付二维码

### 📁 主要配置文件

1. **source/_data/about.yml** - 关于页面数据配置
2. **source/about/index.md** - 关于页面内容
3. **_config.anzhiyu.yml** - 主题配置
4. **_config.yml** - 站点基础配置

### 🔧 需要自定义的配置

#### 1. 51LA统计配置
```yaml
# _config.anzhiyu.yml
LA:
  enable: true
  ck: your_51la_ck_here          # 替换为你的51LA统计代码
  LingQueMonitorID: your_monitor_id_here  # 替换为你的监控ID
```

#### 2. 评论系统配置
```yaml
# _config.anzhiyu.yml
waline:
  serverURL: https://your-waline-server.vercel.app  # 替换为你的Waline服务器地址
```

#### 3. 社交链接配置
```yaml
# _config.anzhiyu.yml
social:
  Github: https://github.com/your-username        # 替换为你的GitHub
  BiliBili: https://space.bilibili.com/your-uid   # 替换为你的B站
  Email: mailto:<EMAIL>            # 替换为你的邮箱
```

#### 4. 个人信息自定义
编辑 `source/_data/about.yml` 文件中的以下内容：
- 个人头像链接
- 姓名和描述
- 技能标签
- 职业信息
- 地理位置
- 兴趣爱好
- 游戏UID
- 番剧列表
- 赞赏名单

### 🖼️ 图片资源说明

当前使用的是Unsplash免费图片作为示例，建议替换为你自己的图片：

1. **头像图片**: `avatarImg` 和 `photo_url`
2. **背景图片**: 各个卡片的背景图
3. **番剧封面**: 番剧列表中的封面图
4. **支付二维码**: 微信和支付宝收款码

### 🚀 启动预览

1. 安装依赖：
```bash
npm install
```

2. 启动本地服务器：
```bash
hexo server
```

3. 访问 http://localhost:4000/about/ 查看效果

### 📝 自定义建议

1. **替换个人信息**: 将所有示例信息替换为你的真实信息
2. **上传个人图片**: 将示例图片替换为你的个人照片
3. **配置统计服务**: 注册51LA并配置统计代码
4. **设置评论系统**: 部署Waline评论服务
5. **调整样式**: 根据个人喜好调整颜色和布局

### 🎨 样式自定义

如需进一步自定义样式，可以：
1. 修改 `_config.anzhiyu.yml` 中的主题色配置
2. 在 `source/css/` 目录下添加自定义CSS
3. 调整 `about.yml` 中的颜色配置

### 📞 技术支持

如果在配置过程中遇到问题，可以：
1. 查看Hexo官方文档
2. 参考AnZhiYu主题文档
3. 检查浏览器控制台错误信息
4. 确保所有配置文件语法正确

---

**注意**: 请确保所有个人信息和链接都已更新为你自己的信息，避免使用示例数据。

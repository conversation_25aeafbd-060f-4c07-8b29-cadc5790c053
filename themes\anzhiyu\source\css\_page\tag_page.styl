#tag #tag-page-tags 
  display: flex
  flex-flow: row wrap

#tag #tag-page-tags a 
  line-height: 1.6
  display: flex
  align-items: center
  color: var(--anzhiyu-fontcolor) !important
  padding: 0.1rem 0.5rem
  margin: 0.25rem
  border-radius: 8px
  border: var(--style-border-always)

  &.selected 
    box-shadow: var(--anzhiyu-shadow-theme)
    color: var(--anzhiyu-white) !important
    background: var(--anzhiyu-theme)
    border: var(--style-border-none)

  &.select .tagsPageCount 
    background: var(--anzhiyu-card-bg)
    color: var(--anzhiyu-lighttext)

#post .tag_share .post-meta__box__tags span.tagsPageCount,
#tag-page-tags .tagsPageCount 
  padding: 4px 6px
  background: var(--anzhiyu-secondbg)
  border: var(--style-border-always)
  min-width: 22.5px
  display: inline-block
  border-radius: 4px
  line-height: 0.6rem
  text-align: center
  font-size: 0.7rem
  color: var(--anzhiyu-fontcolor)
  margin-left: 4px

#tag #tag-page-tags a:hover .tagsPageCount,
#post .tag_share .post-meta__box__tags:hover span.tagsPageCount 
  background: var(--anzhiyu-card-bg)
  color: var(--anzhiyu-lighttext)

#tag #tag-page-tags a 
  font-family: $font-family
  font-size: 0.9em

#tag #tag-page-tags a:hover 
  box-shadow: var(--anzhiyu-shadow-theme)
  color: var(--anzhiyu-white) !important
  background: var(--anzhiyu-theme)
  border: var(--style-border-none)

span.tags-punctuation,
span.categoryes-punctuation 
  margin-right: 4px

span.tags-punctuation .icon-biaoqian,
span.categoryes-punctuation .icon-biaoqian 
  font-size: 13px

.tagsPageCount 
  font-family: $font-family

[data-theme="light"] #post .tag_share .post-meta__box__tags span.tagsPageCount 
  background-color: transparent
  color: white

[data-theme="light"] #post .tag_share .post-meta__box__categoryes span.categoryesPageCount 
  background-color: transparent
  color: white

#post .tag_share .post-meta__box__categoryes span.categoryesPageCount 
  padding: 4px 6px
  background: var(--anzhiyu-secondbg)
  border: var(--style-border-always)
  min-width: 22.5px
  display: inline-block
  border-radius: 4px
  line-height: 0.6rem
  text-align: center
  font-size: 0.7rem
  color: var(--anzhiyu-fontcolor)
  margin-left: 4px

.post-meta__box__categories 
  border-radius: 12px

/* 文章彩色标签 */
/*文章随机彩色标签*/
.post-meta__box a 
  color: var(--anzhiyu-white)
  border: none

[data-theme="dark"] .post-meta__box a 
  opacity: 0.5

[data-theme="dark"] .post-reward .reward-button,
[data-theme="dark"] .reward-link.mode 
  opacity: 0.5

.post-meta__box__tag-list a:nth-child(5n) 
  background-color: #4a4a4a
  color: #fff

.post-meta__box__tag-list a:nth-child(5n + 1) 
  background-color: #ff5e5c
  color: #fff

.post-meta__box__tag-list a:nth-child(5n + 2)
  background-color: #ffbb50
  color: #fff

.post-meta__box__tag-list a:nth-child(5n + 3)
  background-color: #1ac756
  color: #fff

.post-meta__box__tag-list a:nth-child(5n + 4)
  background-color: #19b5fe
  color: #fff

.post-meta__box__tag-list a:hover
  background-color: var(--anzhiyu-main)
  color: #fff
[data-theme="dark"] .post-meta__box__tag-list a:hover
  color: #fff
/* 文章分类颜色背景 */
.post-meta__box__category-list a:nth-child(5n)
  background-color: #4a4a4a
  color: #fff

.post-meta__box__category-list a:nth-child(5n + 1)
  background-color: #1ac756
  color: #fff

.post-meta__box__category-list a:nth-child(5n + 2)
  background-color: #ffbb50
  color: #fff

.post-meta__box__category-list a:nth-child(5n + 3)
  background-color: #19b5fe
  color: #fff

.post-meta__box__category-list a:nth-child(5n + 4)
  background-color: #ff5e5c
  color: #fff



body[data-type="tags"],body[data-type="categories"]
  #page
    border-radius: 12px
    +maxWidth768()
      #tag
        padding: 0px 15px 20px
      .page-title 
        margin: 8px 0 0px
  +maxWidth768()
    #body-wrap .layout
      padding: 0 20px 15px
  #tag #tag-page-tags
    justify-content: center;
    a
      font-size: 1.4rem;
      margin: 0.3rem 0.65rem;
      +maxWidth768()
        font-size: 1.8rem;
        margin: 0.6rem 0.8rem;

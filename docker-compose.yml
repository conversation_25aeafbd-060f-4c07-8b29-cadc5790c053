version: '3.8'

services:
  xing-blog:
    build: .
    container_name: xing-blog
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"  # 如果需要HTTPS
    volumes:
      # 如果需要自定义nginx配置
      - ./deploy/nginx.conf:/etc/nginx/conf.d/default.conf:ro
      # 如果需要SSL证书
      # - ./ssl:/etc/nginx/ssl:ro
    environment:
      - NODE_ENV=production
    networks:
      - blog-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

networks:
  blog-network:
    driver: bridge

# 如果需要数据持久化（比如日志）
volumes:
  nginx-logs:
    driver: local

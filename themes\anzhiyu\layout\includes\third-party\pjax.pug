- var pjaxExclude = 'a:not([target="_blank"])'
if theme.pjax.exclude
  each val in theme.pjax.exclude
    - pjaxExclude = pjaxExclude + `:not([href="${val}"])`

- let pjaxSelectors = ['head > title','#config-diff', '#body-wrap','#rightside-config-hide','#rightside-config-show','.js-pjax']

- let choose = theme.comments.use
if choose
  if theme.Open_Graph_meta && (choose.includes('Twikoo'))
    - pjaxSelectors.unshift('meta[property="og:image"]', 'meta[property="og:title"]', 'meta[property="og:url"]', 'meta[property="og:type"]', 'meta[property="og:site_name"]', 'meta[property="og:description"]')

script(src=url_for(theme.asset.pjax))
script.
  let pjaxSelectors = !{JSON.stringify(pjaxSelectors)}
  var pjax = new Pjax({
    elements: '!{pjaxExclude}',
    selectors: pjaxSelectors,
    cacheBust: false,
    analytics: !{theme.google_analytics ? true : false},
    scrollRestoration: false
  })

  document.addEventListener('pjax:send', function () {
    // removeEventListener scroll 
    anzhiyu.removeGlobalFnEvent('pjax')
    anzhiyu.removeGlobalFnEvent('themeChange')

    document.getElementById('rightside').classList.remove('rightside-show')
    
    if (window.aplayers) {
      for (let i = 0; i < window.aplayers.length; i++) {
        if (!window.aplayers[i].options.fixed) {
          window.aplayers[i].destroy()
        }
      }
    }

    typeof typed === 'object' && typed.destroy()

    //reset readmode
    const $bodyClassList = document.body.classList
    $bodyClassList.contains('read-mode') && $bodyClassList.remove('read-mode')
  })

  document.addEventListener('pjax:complete', function () {
    window.refreshFn()

    document.querySelectorAll('script[data-pjax]').forEach(item => {
      const newScript = document.createElement('script')
      const content = item.text || item.textContent || item.innerHTML || ""
      Array.from(item.attributes).forEach(attr => newScript.setAttribute(attr.name, attr.value))
      newScript.appendChild(document.createTextNode(content))
      item.parentNode.replaceChild(newScript, item)
    })

    GLOBAL_CONFIG.islazyload && window.lazyLoadInstance.update()

    typeof panguInit === 'function' && panguInit()

    // google analytics
    typeof gtag === 'function' && gtag('config', '!{theme.google_analytics}', {'page_path': window.location.pathname});

    // baidu analytics
    typeof _hmt === 'object' && _hmt.push(['_trackPageview',window.location.pathname]);

    typeof loadMeting === 'function' && document.getElementsByClassName('aplayer').length && loadMeting()

    // prismjs
    typeof Prism === 'object' && Prism.highlightAll()
  })

  document.addEventListener('pjax:error', e => {
    if (e.request.status === 404) {
      pjax.loadUrl('!{url_for("/404.html")}')
    }
  })

# 朋友圈页面配置说明

## 📋 配置完成项目

已成功配置了朋友圈页面，参考了安知鱼主题官方文档的配置方法。

### ✅ 已完成的配置

1. **朋友圈页面创建**
   - 创建了 `source/fcircle/index.md` 页面
   - 设置了正确的 front-matter 配置
   - 添加了朋友圈容器和加载动画

2. **菜单导航配置**
   - 在 `_config.anzhiyu.yml` 中启用了朋友圈菜单项
   - 路径：友链 → 朋友圈 (/fcircle/)
   - 图标：anzhiyu-icon-artstation

3. **朋友圈功能配置**
   - 启用了 `friends_vue.enable: true`
   - 配置了前端 Vue.js 文件
   - 设置了顶部背景图片
   - 添加了提示文字

### 📁 相关文件

1. **source/fcircle/index.md** - 朋友圈页面
2. **source/link/index.md** - 友链页面
3. **source/_data/link.yml** - 友链数据
4. **_config.anzhiyu.yml** - 主题配置

### 🔧 当前配置详情

#### 朋友圈页面配置 (source/fcircle/index.md)
```markdown
---
title: 朋友圈
date: 2025-08-14 14:54:26
comments: false
aside: false
top_img: false
type: "fcircle"
---
```

#### 主题配置 (_config.anzhiyu.yml)
```yaml
# 朋友圈配置
friends_vue:
  enable: true
  vue_js: https://npm.elemecdn.com/anzhiyu-theme-static@1.1.2/friends/index.f9a2b8d2.js
  apiurl: https://friends-api.anheyu.com/ # 朋友圈后端地址
  top_tips: 使用 友链朋友圈 订阅友链最新文章
  top_background: https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=1200&h=600&fit=crop
```

#### 菜单配置
```yaml
menu:
  友链:
    友人帐: /link/ || anzhiyu-icon-link
    朋友圈: /fcircle/ || anzhiyu-icon-artstation
    留言板: /comments/ || anzhiyu-icon-envelope
```

### 🚀 访问地址

- **朋友圈页面**: http://localhost:4001/fcircle/
- **友链页面**: http://localhost:4001/link/
- **留言板页面**: http://localhost:4001/comments/

### ⚠️ 重要说明

#### 朋友圈后端服务
当前使用的是示例后端地址 `https://friends-api.anheyu.com/`，这是一个演示地址。

**如需完整功能，你需要：**

1. **部署朋友圈后端服务**
   - 参考：https://github.com/anzhiyu-c/hexo-circle-of-friends
   - 或使用其他兼容的朋友圈后端服务

2. **替换后端地址**
   - 将 `apiurl` 替换为你自己的后端服务地址
   - 确保后端服务支持 common2 格式的友链数据

3. **配置友链数据**
   - 在 `source/_data/link.yml` 中添加更多友链
   - 朋友圈会自动抓取这些友链的最新文章

### 🎨 自定义选项

#### 1. 更换背景图片
```yaml
top_background: https://your-custom-background.jpg
```

#### 2. 自定义提示文字
```yaml
top_tips: 你的自定义提示文字
```

#### 3. 使用自定义前端文件
如果需要自定义前端逻辑：
1. 下载 `https://npm.elemecdn.com/anzhiyu-theme-static@1.1.2/friends/index.f9a2b8d2.js`
2. 修改其中的 API 地址
3. 上传到你的 CDN
4. 更新 `vue_js` 配置

### 📊 功能特点

- ✅ 自动抓取友链最新文章
- ✅ 响应式设计
- ✅ 美观的卡片布局
- ✅ 加载动画效果
- ✅ 文章时间排序
- ✅ 友链站点分类显示

### 🔗 相关链接

- [安知鱼主题文档 - 朋友圈配置](https://docs.anheyu.com/page/fcircle.html)
- [朋友圈后端项目](https://github.com/anzhiyu-c/hexo-circle-of-friends)
- [朋友圈前端项目](https://github.com/anzhiyu-c/hexo-circle-of-friends-front)

### 🛠️ 故障排除

如果朋友圈页面显示异常：
1. 检查后端服务是否正常运行
2. 确认友链数据格式是否正确
3. 查看浏览器控制台是否有错误信息
4. 确保网络可以访问后端 API

---

**注意**: 朋友圈功能需要后端服务支持，当前使用的是演示地址。如需完整功能，请部署自己的朋友圈后端服务。

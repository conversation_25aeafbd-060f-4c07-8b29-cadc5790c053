# AI 摘要功能配置说明

## 📋 功能概述

安知鱼博客主题现已支持 OpenAI 兼容的 AI 摘要功能，可以为您的文章自动生成智能摘要。

## 🔧 配置步骤

### 1. 主题配置文件设置

在 `_config.anzhiyu.yml` 中找到 `post_head_ai_description` 配置：

```yaml
# 文章顶部ai摘要
post_head_ai_description:
  enable: true
  gptName: 郁离AI助手
  mode: openai # 使用OpenAI兼容模式 可选值: tianli/local/openai
  switchBtn: true # 可以配置是否显示切换按钮 以切换tianli/local
  btnLink: https://afdian.net/item/886a79d4db6711eda42a52540025c377
  randomNum: 5 # 按钮最大的随机次数，也就是一篇文章最大随机出来几种
  basicWordCount: 1000 # 最低获取字符数, 最小1000, 最大1999
  # OpenAI 兼容配置
  openai:
    apiKey: sk-your-api-key-here # 请替换为您的 OpenAI 兼容 API Key
    apiUrl: https://api.openai.com/v1/chat/completions # 请替换为您的 OpenAI 兼容 API 端点
    model: gpt-3.5-turbo # 使用的模型名称，如: gpt-3.5-turbo, gpt-4, claude-3-sonnet 等
    maxTokens: 500 # 摘要最大 token 数
    temperature: 0.7 # 创造性参数 0-1，数值越高越有创造性
    systemPrompt: "你是一个专业的技术博客文章摘要助手。请为以下文章生成一个简洁、准确的中文摘要，突出文章的核心内容和技术要点。摘要应该在100-200字之间，语言要通俗易懂。"
  # 备用天理配置（如果需要）
  key: your-tianli-key-here
  Referer: https://youke1.picui.cn/
```

### 2. 支持的 API 提供商

#### OpenAI 官方
```yaml
apiKey: sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
apiUrl: https://api.openai.com/v1/chat/completions
model: gpt-3.5-turbo
```

#### Azure OpenAI
```yaml
apiKey: your-azure-api-key
apiUrl: https://your-resource.openai.azure.com/openai/deployments/your-deployment/chat/completions?api-version=2023-12-01-preview
model: gpt-35-turbo
```

#### 其他兼容提供商（如 Anthropic Claude、国内厂商等）
```yaml
apiKey: your-api-key
apiUrl: https://your-provider.com/v1/chat/completions
model: claude-3-sonnet-20240229
```

### 3. 文章配置

在需要启用 AI 摘要的文章 Front Matter 中添加：

```yaml
---
title: 您的文章标题
date: 2025-04-18
ai: true  # 启用 AI 摘要
---
```

### 4. CDN 配置

确保在 `_config.anzhiyu.yml` 的 CDN 配置中启用了自定义 AI 脚本：

```yaml
CDN:
  option:
    ai_abstract_js: /js/anzhiyu/ai_abstract_openai.js
```

## 🎯 功能特性

- ✅ **多模型支持**：支持 GPT-3.5、GPT-4、Claude 等多种模型
- ✅ **智能摘要**：基于文章内容自动生成准确摘要
- ✅ **中文优化**：专门针对中文技术博客优化
- ✅ **错误处理**：完善的错误提示和异常处理
- ✅ **动画效果**：打字机效果的摘要显示
- ✅ **交互功能**：支持重新生成、朗读等功能

## 🔍 测试方法

1. 配置完成后运行 `hexo clean && hexo generate`
2. 启动本地服务器 `hexo server`
3. 访问启用了 `ai: true` 的文章页面
4. 查看文章顶部是否出现 AI 摘要模块
5. 点击"生成本文简介"按钮测试摘要生成

## ⚠️ 注意事项

1. **API Key 安全**：请妥善保管您的 API Key，不要提交到公开仓库
2. **费用控制**：AI 摘要会消耗 API 调用次数，请注意费用控制
3. **网络要求**：需要能够访问您配置的 API 端点
4. **内容长度**：文章内容过短可能影响摘要质量
5. **模型选择**：不同模型的效果和费用不同，请根据需求选择

## 🐛 故障排除

### 常见错误及解决方案

1. **401 API Key 无效**
   - 检查 API Key 是否正确
   - 确认 API Key 是否有效且未过期

2. **429 请求频率过高**
   - 降低测试频率
   - 检查 API 提供商的频率限制

3. **网络连接错误**
   - 检查网络连接
   - 确认 API 端点地址是否正确

4. **摘要生成失败**
   - 检查文章内容是否足够长
   - 确认模型配置是否正确

## 📝 自定义提示词

您可以根据博客类型自定义 `systemPrompt`：

### 技术博客
```
你是一个专业的技术博客文章摘要助手。请为以下文章生成一个简洁、准确的中文摘要，突出文章的核心内容和技术要点。摘要应该在100-200字之间，语言要通俗易懂。
```

### 生活博客
```
你是一个生活博客文章摘要助手。请为以下文章生成一个温馨、有趣的中文摘要，突出文章的情感和生活感悟。摘要应该在100-200字之间，语言要亲切自然。
```

### 学术博客
```
你是一个学术博客文章摘要助手。请为以下文章生成一个严谨、准确的中文摘要，突出文章的研究内容和学术价值。摘要应该在100-200字之间，语言要专业规范。
```

## 🎉 完成

配置完成后，您的博客就拥有了智能 AI 摘要功能！读者可以快速了解文章核心内容，提升阅读体验。

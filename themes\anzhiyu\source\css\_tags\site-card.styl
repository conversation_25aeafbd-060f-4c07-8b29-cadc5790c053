trans($time = 0.28s)
  transition: all $time ease
  -moz-transition: all $time ease
  -webkit-transition: all $time ease
  -o-transition: all $time ease


.site-card-group
  display: flex
  flex-wrap: wrap
  justify-content: flex-start
  margin: -0.5 * 16px
  align-items: stretch
.site-card
  margin: 16px * 0.5
  width: "calc(100% / 4 - %s)" % 16px
  @media screen and (min-width: 2048px)
      width: "calc(100% / 5 - %s)" % 16px
  @media screen and (max-width: 768px)
      width: "calc(100% / 3 - %s)" % 16px
  @media screen and (max-width: 500px)
      width: "calc(100% / 2 - %s)" % 16px
  display: block
  line-height: 1.4
  height 100%
  .img
    width: 100%
    height 120px
    @media screen and (max-width: 500px)
      height 100px
    overflow: hidden
    border-radius: 12px * 0.5
    box-shadow: 0 1px 2px 0px rgba(0, 0, 0, 0.2)
    background: #f6f6f6
    img
      width: 100%
      height 100%
      pointer-events:none;
      // trans(.75s)
      transition: transform 2s ease
      object-fit: cover

  .info
    margin-top: 16px * 0.5
    img
      width: 32px
      height: 32px
      pointer-events:none;
      border-radius: 16px
      float: left
      margin-right: 8px
      margin-top: 2px
    span
      display: block
    .title
      font-weight: 600
      font-size: var(--global-font-size)
      color: #444
      display: -webkit-box
      -webkit-box-orient: vertical
      overflow: hidden
      -webkit-line-clamp: 1
      trans()
    .desc
      font-size: var(--global-font-size)
      word-wrap: break-word;
      line-height: 1.2
      color: #888
      display: -webkit-box
      -webkit-box-orient: vertical
      overflow: hidden
      -webkit-line-clamp: 2
  .img
    trans()
  &:hover
    .img
      box-shadow: 0 4px 8px 0px rgba(0, 0, 0, 0.1), 0 2px 4px 0px rgba(0, 0, 0, 0.1), 0 4px 8px 0px rgba(0, 0, 0, 0.1), 0 8px 16px 0px rgba(0, 0, 0, 0.1)
    .info .title
      color: #ff5722

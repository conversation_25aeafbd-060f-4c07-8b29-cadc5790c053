.checkbox
  display flex
  align-items center
  input
    -webkit-appearance none
    -moz-appearance none
    -ms-appearance none
    -o-appearance none
    appearance none
    position relative
    height 16px
    width 16px
    transition all 0.15s ease-out 0s
    cursor pointer
    display inline-block
    outline none
    border-radius 2px
    flex-shrink 0
    margin-right 8px
    border 2px solid #2196f3
    pointer-events: none
  input[type="checkbox"]:before
    left 1px
    top 5px
    width 0
    height 2px
    transition all 0.2s ease-in
    transform rotate(45deg)
    -webkit-transform rotate(45deg)
    -moz-transform rotate(45deg)
    -ms-transform rotate(45deg)
    -o-transform rotate(45deg)
  input[type="checkbox"]:after
    right 7px
    bottom 3px
    width 2px
    height 0
    transition all 0.2s ease-out
    transform rotate(40deg)
    -webkit-transform rotate(40deg)
    -moz-transform rotate(40deg)
    -ms-transform rotate(40deg)
    -o-transform rotate(40deg)
    transition-delay 0.25s
  input[type="checkbox"]:checked
    background #2196f3
    &:before
      left 0
      top 7px
      width 6px
      height 2px
    &:after
      right 3px
      bottom 1px
      width 2px
      height 10px
  &.minus
    input[type="checkbox"]:before
      transform rotate(0)
      left 1px
      top 5px
      width 0
      height 2px
    input[type="checkbox"]:after
      transform rotate(0)
      left 1px
      top 5px
      width 0
      height 2px
    input[type="checkbox"]:checked
      &:before
        left 1px
        top 5px
        width 10px
        height 2px
      &:after
        left 1px
        top 5px
        width 10px
        height 2px
  &.plus
    input[type="checkbox"]:before
      transform rotate(0)
      left 1px
      top 5px
      width 0
      height 2px
    input[type="checkbox"]:after
      transform rotate(0)
      left 5px
      top 1px
      width 2px
      height 0
    input[type="checkbox"]:checked
      &:before
        left 1px
        top 5px
        width 10px
        height 2px
      &:after
        left 5px
        top 1px
        width 2px
        height 10px
  &.times
    input[type="checkbox"]:before
      transform rotate(45deg)
      left 3px
      top 1px
      width 0
      height 2px
    input[type="checkbox"]:after
      transform rotate(135deg)
      right 3px
      top 1px
      width 0
      height 2px
    input[type="checkbox"]:checked
      &:before
        left 1px
        top 5px
        width 10px
        height 2px
      &:after
        right 1px
        top 5px
        width 10px
        height 2px
  input[type="radio"]
    border-radius 50%
  input[type="radio"]:before
    content ""
    display block
    width 8px
    height 8px
    border-radius 50%
    margin 2px
    transform scale(0)
    transition all 0.25s ease-out
  input[type="radio"]:checked
    &:before
      transform scale(1)
      background var(--text-bg-hover)
  &.red
    input
      border-color #fe5f58
    input[type="checkbox"]:checked
      background #fe5f58
    input[type="radio"]:checked
      &:before
        background #fe5f58
  &.green
    input
      border-color #3dc550
    input[type="checkbox"]:checked
      background #3dc550
    input[type="radio"]:checked
      &:before
        background #3dc550
  &.yellow
    input
      border-color #ffbd2b
    input[type="checkbox"]:checked
      background #ffbd2b
    input[type="radio"]:checked
      &:before
        background #ffbd2b
  &.cyan
    input
      border-color #1bcdfc
    input[type="checkbox"]:checked
      background #1bcdfc
    input[type="radio"]:checked
      &:before
        background #1bcdfc
  &.blue
    input
      border-color #2196f3
    input[type="checkbox"]:checked
      background #2196f3
    input[type="radio"]:checked
      &:before
        background #2196f3
  p
    display inline-block
    margin-top 2px !important
    margin-bottom 0 !important

.checkbox input[type="checkbox"]:before,
.checkbox input[type="checkbox"]:after
  position absolute
  content ""
  background #fff

# 📝 Xing Blog 编辑发布完整流程指南

## 🎯 概述

本文档详细介绍从本地编辑、实时预览到发布上线的完整博客工作流程，让你能够高效地管理和发布博客内容。

## 🚀 完整工作流程

### 1️⃣ 启动本地实时预览服务器

#### 方法一：标准命令
```bash
# 启动默认端口4000的服务器
hexo server

# 指定端口（如果4000端口被占用）
hexo server -p 4001

# 启动并显示草稿
hexo server --draft
```

#### 方法二：使用快速脚本（推荐）
```bash
# 一键预览（自动生成+启动服务器）
./quick-deploy.sh --preview

# 交互式选择
./quick-deploy.sh
# 然后选择 "1) 本地预览"
```

#### 服务器启动成功标志
```
INFO  Hexo is running at http://localhost:4000/ . Press Ctrl+C to stop.
```

### 2️⃣ 创建和编辑文章

#### 创建新文章
```bash
# 创建普通文章
hexo new "我的新文章标题"

# 创建草稿
hexo new draft "未完成的想法"

# 指定布局
hexo new page "关于我"
```

#### 编辑文章内容
```bash
# 使用VS Code编辑（推荐）
code "source/_posts/我的新文章标题.md"

# 使用其他编辑器
notepad "source/_posts/我的新文章标题.md"    # Windows记事本
nano "source/_posts/我的新文章标题.md"       # 终端编辑器
vim "source/_posts/我的新文章标题.md"        # Vim编辑器
```

#### 文章头部信息模板
```yaml
---
title: 文章标题
date: 2025-08-15 12:54:28
updated: 2025-08-15 12:54:28
tags: 
  - 技术
  - 学习
  - 前端
categories: 
  - 技术分享
  - 前端开发
description: 这里是文章的简短描述，会显示在首页和搜索结果中
cover: /images/cover.jpg        # 文章封面图（可选）
top_img: /images/top_img.jpg    # 顶部大图（可选）
sticky: 1                       # 置顶权重（可选）
---
```

### 3️⃣ 实时预览和调试

#### 🔄 热重载功能
- **自动刷新**：保存文件后，浏览器自动刷新显示最新内容
- **实时同步**：无需重启服务器，所见即所得
- **多设备预览**：可以在手机、平板上访问 `http://你的IP:4000` 预览

#### 🌐 预览地址
- **本地访问**：http://localhost:4000
- **局域网访问**：http://你的IP地址:4000
- **移动端测试**：使用浏览器开发者工具或真机访问

#### 📱 响应式测试
```bash
# 在浏览器中按F12打开开发者工具
# 点击设备图标测试不同屏幕尺寸
# 常见测试尺寸：
# - 手机：375x667 (iPhone)
# - 平板：768x1024 (iPad)
# - 桌面：1920x1080
```

### 4️⃣ 发布前检查清单

在发布到线上之前，建议逐项检查：

#### ✅ 内容检查
- [ ] 文章标题准确无误
- [ ] 正文内容完整，无错别字
- [ ] 代码块语法高亮正常
- [ ] 链接可以正常访问
- [ ] 图片显示正常，路径正确

#### ✅ 格式检查
- [ ] Markdown语法正确
- [ ] 标题层级合理（H1-H6）
- [ ] 列表格式正确
- [ ] 引用块显示正常

#### ✅ 元数据检查
- [ ] 标签和分类设置合理
- [ ] 文章描述简洁明了
- [ ] 发布日期正确
- [ ] 封面图片合适

#### ✅ 功能检查
- [ ] 音乐播放器正常工作
- [ ] 评论系统可用（如果启用）
- [ ] 分享功能正常
- [ ] 搜索功能可以找到文章

### 5️⃣ 发布到VPS服务器

#### 🎯 方法一：一键部署（推荐）

```bash
# 停止本地预览服务器
# 按 Ctrl+C 停止

# 一键部署到服务器
./quick-deploy.sh --deploy

# 或使用交互式菜单
./quick-deploy.sh
# 选择 "2) 生成并部署到服务器"
# 确认部署时输入 y
```

#### 🔧 方法二：手动部署

```bash
# 1. 停止本地服务器
# 按 Ctrl+C

# 2. 清理并生成静态文件
hexo clean && hexo generate

# 3. 检查生成结果
ls -la public/
# 确保public目录包含index.html等文件

# 4. 创建部署包
TIMESTAMP=$(date +%Y%m%d-%H%M%S)
tar -czf "blog-${TIMESTAMP}.tar.gz" public/

# 5. 上传到服务器
scp "blog-${TIMESTAMP}.tar.gz" root@************:/tmp/

# 6. 在服务器上部署
ssh root@************ << 'EOF'
cd /tmp
sudo mkdir -p /var/www/xing-blog
sudo rm -rf /var/www/xing-blog/*
sudo tar -xzf blog-*.tar.gz -C /var/www/xing-blog --strip-components=1
sudo chown -R www-data:www-data /var/www/xing-blog
sudo chmod -R 755 /var/www/xing-blog
sudo nginx -t && sudo systemctl reload nginx
rm -f blog-*.tar.gz
echo "部署完成！"
EOF

# 7. 清理本地文件
rm -f "blog-${TIMESTAMP}.tar.gz"
```

### 6️⃣ 验证发布结果

#### 🌐 访问线上网站
```bash
# 主域名访问
https://an.xing2006.me

# 检查新文章是否出现在首页
# 点击进入文章页面验证内容
```

#### 🔍 服务器状态检查
```bash
# 连接服务器检查状态
ssh root@************

# 检查Nginx状态
sudo systemctl status nginx

# 查看访问日志
sudo tail -f /var/log/nginx/xing-blog.access.log

# 查看错误日志
sudo tail -f /var/log/nginx/xing-blog.error.log

# 检查网站文件
ls -la /var/www/xing-blog/
```

## 📋 日常工作流程示例

### 🌅 开始新的写作会话

```bash
# 1. 进入项目目录
cd /d/2048/022/anzhiyu-blog

# 2. 启动预览服务器
hexo server
# 浏览器访问 http://localhost:4000

# 3. 创建今天的文章
hexo new "$(date +%Y-%m-%d) 学习笔记"

# 4. 开始编辑
code "source/_posts/$(date +%Y-%m-%d)-学习笔记.md"
```

### ✍️ 编辑写作过程

```bash
# 编辑循环：
# 1. 在编辑器中写内容
# 2. 保存文件 (Ctrl+S)
# 3. 浏览器自动刷新查看效果
# 4. 继续编辑优化
# 5. 重复直到满意
```

### 🚀 完成发布

```bash
# 1. 最终检查
# 在浏览器中完整浏览一遍文章

# 2. 停止预览服务器
# 按 Ctrl+C

# 3. 发布到线上
./quick-deploy.sh --deploy

# 4. 验证发布结果
# 访问 https://an.xing2006.me 确认文章已上线
```

## 🎯 快捷命令速查表

| 操作 | 命令 | 说明 |
|------|------|------|
| **文章管理** |
| 创建文章 | `hexo new "标题"` | 创建新文章 |
| 创建草稿 | `hexo new draft "标题"` | 创建草稿 |
| 发布草稿 | `hexo publish "标题"` | 将草稿发布为正式文章 |
| **本地预览** |
| 启动服务器 | `hexo server` | 启动本地预览服务器 |
| 指定端口 | `hexo server -p 4001` | 使用指定端口 |
| 包含草稿 | `hexo server --draft` | 预览时包含草稿 |
| 快速预览 | `./quick-deploy.sh -p` | 生成+预览 |
| **文件操作** |
| 清理缓存 | `hexo clean` | 清理生成的文件和缓存 |
| 生成文件 | `hexo generate` | 生成静态网站文件 |
| 完整重建 | `hexo clean && hexo generate` | 清理后重新生成 |
| **部署发布** |
| 一键部署 | `./quick-deploy.sh -d` | 生成+部署到服务器 |
| 交互部署 | `./quick-deploy.sh` | 交互式选择操作 |
| **服务器管理** |
| 停止服务器 | `Ctrl+C` | 停止本地预览服务器 |
| 查看日志 | `hexo server --debug` | 显示详细调试信息 |

## 🔧 高级功能和技巧

### 📝 草稿工作流
```bash
# 创建草稿
hexo new draft "想法还不成熟的文章"

# 预览草稿（草稿不会在正常预览中显示）
hexo server --draft

# 草稿完善后发布
hexo publish "想法还不成熟的文章"
```

### 🎨 主题自定义
```bash
# 修改主题配置文件
nano _config.anzhiyu.yml

# 修改后需要重启服务器才能看到效果
# 1. 停止服务器 (Ctrl+C)
# 2. 重新启动
hexo server
```

### 📊 性能优化
```bash
# 生成时显示详细信息
hexo generate --debug

# 监听文件变化（开发模式）
hexo server --watch

# 静态文件压缩（如果配置了相关插件）
hexo generate --compress
```

### 🔍 调试技巧
```bash
# 查看详细日志
hexo server --debug

# 检查配置文件语法
hexo config

# 列出所有文章
hexo list post

# 列出所有页面
hexo list page
```

## 🐛 常见问题解决

### 问题1：端口被占用
```bash
# 查看端口占用情况
netstat -ano | findstr :4000    # Windows
lsof -i :4000                   # Linux/Mac

# 解决方案：使用其他端口
hexo server -p 4001
```

### 问题2：文章不显示或显示异常
```bash
# 清理缓存重新生成
hexo clean
hexo generate
hexo server

# 检查文章头部信息格式
# 确保YAML格式正确，注意缩进
```

### 问题3：图片不显示
```bash
# 检查图片路径
# 正确路径：/images/图片名.jpg
# 图片文件应放在：source/images/目录下

# 或使用相对路径
# ![描述](../images/图片名.jpg)
```

### 问题4：中文文件名问题
```bash
# 避免使用特殊字符作为文件名
# 推荐使用英文文件名，中文作为title

# 例如：
hexo new "my-first-post"
# 然后在文件中设置 title: "我的第一篇文章"
```

### 问题5：部署失败
```bash
# 检查SSH连接
ssh root@************

# 检查服务器磁盘空间
df -h

# 检查Nginx配置
sudo nginx -t

# 查看详细错误信息
./quick-deploy.sh --deploy --verbose
```

## 📚 最佳实践建议

### ✍️ 写作建议
1. **标题要吸引人**：简洁明了，包含关键词
2. **结构要清晰**：使用标题层级组织内容
3. **配图要合适**：图片大小适中，格式统一
4. **标签要准确**：便于分类和搜索
5. **描述要简洁**：一句话概括文章内容

### 🔄 工作流建议
1. **定期备份**：重要文章要有备份
2. **版本控制**：使用Git管理文章版本
3. **预览充分**：发布前多设备测试
4. **SEO优化**：合理使用标题和关键词
5. **定期更新**：保持博客活跃度

### 🚀 性能优化
1. **图片压缩**：使用适当大小的图片
2. **代码高亮**：选择合适的代码主题
3. **缓存利用**：合理设置浏览器缓存
4. **CDN加速**：静态资源使用CDN
5. **定期清理**：删除不需要的文件

---

## 🎉 结语

通过这个完整的工作流程，你可以高效地管理博客内容，从本地编辑到线上发布都变得简单快捷。记住关键步骤：

**编辑 → 预览 → 检查 → 发布 → 验证**

祝你写作愉快！🎊

## 📎 附录

### A. 常用Markdown语法速查

```markdown
# 一级标题
## 二级标题
### 三级标题

**粗体文本**
*斜体文本*
~~删除线~~

- 无序列表项1
- 无序列表项2

1. 有序列表项1
2. 有序列表项2

[链接文本](https://example.com)
![图片描述](/images/image.jpg)

`行内代码`

```javascript
// 代码块
console.log('Hello World');
```

> 引用文本

| 表头1 | 表头2 |
|-------|-------|
| 内容1 | 内容2 |
```

### B. 安知鱼主题特殊标签

```markdown
<!-- 提示框 -->
{% note success %}
成功提示内容
{% endnote %}

{% note info %}
信息提示内容
{% endnote %}

{% note warning %}
警告提示内容
{% endnote %}

{% note danger %}
危险提示内容
{% endnote %}

<!-- 代码块 -->
{% codeblock lang:javascript %}
console.log('Hello World');
{% endcodeblock %}

<!-- 链接卡片 -->
{% link 标题,链接,描述 %}

<!-- 按钮 -->
{% btn 链接,文本,图标,颜色 %}

<!-- 标签 -->
{% label 文本 color:颜色 %}

<!-- 时间轴 -->
{% timeline 标题 %}
{% timenode 时间 %}
内容
{% endtimenode %}
{% endtimeline %}
```

### C. 文件目录结构说明

```
anzhiyu-blog/
├── _config.yml                 # 博客主配置文件
├── _config.anzhiyu.yml         # 主题配置文件
├── package.json                # 项目依赖配置
├── source/                     # 源文件目录
│   ├── _posts/                # 文章目录
│   ├── _data/                 # 数据文件
│   ├── images/                # 图片资源
│   ├── json/                  # JSON数据文件
│   │   └── music.json         # 音乐列表
│   └── about/                 # 关于页面
├── themes/                     # 主题目录
│   └── anzhiyu/              # 安知鱼主题
├── public/                     # 生成的静态文件（部署用）
├── node_modules/              # 项目依赖
├── quick-deploy.sh            # 快速部署脚本
├── quick-deploy.bat           # Windows部署脚本
└── 博客编辑发布完整流程.md    # 本文档
```

### D. 服务器文件结构

```
VPS服务器 (************)
├── /var/www/xing-blog/        # 网站文件目录
│   ├── index.html            # 首页
│   ├── css/                  # 样式文件
│   ├── js/                   # JavaScript文件
│   ├── images/               # 图片文件
│   └── ...                   # 其他静态文件
├── /etc/nginx/               # Nginx配置
│   ├── sites-available/
│   │   └── xing-blog         # 网站配置文件
│   └── sites-enabled/
│       └── xing-blog         # 启用的配置链接
└── /var/log/nginx/           # 日志文件
    ├── xing-blog.access.log  # 访问日志
    └── xing-blog.error.log   # 错误日志
```

### E. 环境变量和配置

```bash
# 设置Git用户信息（首次使用）
git config --global user.name "你的名字"
git config --global user.email "你的邮箱"

# 设置SSH密钥（用于服务器连接）
ssh-keygen -t rsa -b 4096 -C "你的邮箱"
ssh-copy-id root@************

# Node.js版本管理（推荐使用nvm）
nvm install 18
nvm use 18
```

### F. 备份和恢复

```bash
# 备份博客源文件
tar -czf blog-backup-$(date +%Y%m%d).tar.gz \
  source/ themes/ _config*.yml package.json

# 备份服务器网站文件
ssh root@************ \
  "tar -czf /tmp/website-backup-$(date +%Y%m%d).tar.gz -C /var/www xing-blog"

# 下载服务器备份
scp root@************:/tmp/website-backup-*.tar.gz ./

# 恢复备份
tar -xzf blog-backup-*.tar.gz
npm install
hexo generate
```

### G. 性能监控命令

```bash
# 检查网站响应时间
curl -w "@curl-format.txt" -o /dev/null -s https://an.xing2006.me

# 检查SSL证书状态
openssl s_client -connect an.xing2006.me:443 -servername an.xing2006.me

# 检查服务器资源使用
ssh root@************ "top -n 1 -b | head -20"
ssh root@************ "df -h"
ssh root@************ "free -h"
```

---

## 🆘 技术支持

### 📞 获取帮助
- **Hexo官方文档**：https://hexo.io/docs/
- **安知鱼主题文档**：https://docs.anheyu.com/
- **Markdown语法指南**：https://markdown.com.cn/
- **Git使用教程**：https://git-scm.com/docs

### 🐛 问题反馈
如果遇到问题，请提供以下信息：
1. 操作系统版本
2. Node.js版本 (`node --version`)
3. Hexo版本 (`hexo version`)
4. 具体错误信息
5. 操作步骤

### 💡 改进建议
欢迎提出改进建议，让这个工作流程更加完善！

---

**祝你写作愉快！** 🎊

*最后更新：2025年8月15日*

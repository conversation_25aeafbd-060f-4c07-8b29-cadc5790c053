@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

echo.
echo ========================================
echo    AnZhiYu博客自动部署脚本
echo ========================================
echo.

REM 配置参数 - 请根据您的服务器信息修改
set SERVER_IP=your-server-ip
set USERNAME=your-username
set BLOG_PATH=/var/www/anzhiyu-blog
set BACKUP_PATH=/var/backups/blog-backup-%date:~0,4%%date:~5,2%%date:~8,2%-%time:~0,2%%time:~3,2%%time:~6,2%

echo 请输入服务器信息（直接回车使用默认值）:
echo.
set /p INPUT_SERVER=服务器IP [%SERVER_IP%]: 
if not "%INPUT_SERVER%"=="" set SERVER_IP=%INPUT_SERVER%

set /p INPUT_USER=用户名 [%USERNAME%]: 
if not "%INPUT_USER%"=="" set USERNAME=%INPUT_USER%

set /p INPUT_PATH=博客路径 [%BLOG_PATH%]: 
if not "%INPUT_PATH%"=="" set BLOG_PATH=%INPUT_PATH%

echo.
echo 🚀 开始部署到服务器...
echo 服务器: %USERNAME%@%SERVER_IP%
echo 路径: %BLOG_PATH%
echo.

REM 检查git状态
echo 📋 检查本地git状态...
git status --porcelain > temp_status.txt
for /f %%i in (temp_status.txt) do (
    echo ⚠️  警告: 本地有未提交的更改
    git status --short
    set /p CONTINUE=是否继续部署? (y/N): 
    if /i not "!CONTINUE!"=="y" (
        echo ❌ 部署已取消
        del temp_status.txt
        pause
        exit /b 1
    )
    goto :continue
)
:continue
del temp_status.txt

REM 推送到远程仓库
echo 📤 推送到远程仓库...
git push origin master 2>nul || echo ⚠️  无法推送到远程仓库，继续部署...

echo.
echo 🔗 连接服务器并开始部署...
echo 💡 请确保已配置SSH密钥认证或准备输入密码
echo.

REM 创建临时SSH脚本
echo set -e > temp_deploy.sh
echo echo "📍 当前在服务器上操作..." >> temp_deploy.sh
echo if [ ! -d "%BLOG_PATH%" ]; then >> temp_deploy.sh
echo   echo "❌ 博客目录不存在: %BLOG_PATH%" >> temp_deploy.sh
echo   exit 1 >> temp_deploy.sh
echo fi >> temp_deploy.sh
echo cd %BLOG_PATH% >> temp_deploy.sh
echo echo "⏹️  停止当前服务..." >> temp_deploy.sh
echo pm2 stop hexo-blog 2^>/dev/null ^|^| pkill -f "hexo server" 2^>/dev/null ^|^| echo "没有运行的服务" >> temp_deploy.sh
echo echo "💾 备份当前版本..." >> temp_deploy.sh
echo mkdir -p $(dirname %BACKUP_PATH%) >> temp_deploy.sh
echo cp -r %BLOG_PATH% %BACKUP_PATH% >> temp_deploy.sh
echo echo "📥 拉取最新代码..." >> temp_deploy.sh
echo git pull origin master >> temp_deploy.sh
echo echo "📦 安装/更新依赖..." >> temp_deploy.sh
echo npm install --production >> temp_deploy.sh
echo echo "🧹 清理缓存..." >> temp_deploy.sh
echo npx hexo clean >> temp_deploy.sh
echo echo "🔨 生成静态文件..." >> temp_deploy.sh
echo npx hexo generate >> temp_deploy.sh
echo echo "🔄 重启服务..." >> temp_deploy.sh
echo pm2 start ecosystem.config.js 2^>/dev/null ^|^| pm2 start hexo-blog 2^>/dev/null ^|^| { >> temp_deploy.sh
echo   echo "🚀 启动 Hexo 服务器..." >> temp_deploy.sh
echo   nohup npx hexo server ^> hexo.log 2^>^&1 ^& >> temp_deploy.sh
echo   echo $! ^> hexo.pid >> temp_deploy.sh
echo } >> temp_deploy.sh
echo echo "✅ 部署完成!" >> temp_deploy.sh
echo echo "📊 服务状态:" >> temp_deploy.sh
echo pm2 status 2^>/dev/null ^|^| ps aux ^| grep hexo ^| grep -v grep ^|^| echo "服务可能未正常启动" >> temp_deploy.sh

REM 执行SSH部署
ssh %USERNAME%@%SERVER_IP% "bash -s" < temp_deploy.sh

REM 清理临时文件
del temp_deploy.sh

echo.
echo ========================================
echo 🎉 部署完成!
echo ========================================
echo.
echo 📝 部署摘要:
echo - 服务器: %USERNAME%@%SERVER_IP%
echo - 路径: %BLOG_PATH%
echo - 备份: %BACKUP_PATH%
echo.
echo 🌐 请访问您的网站检查更新效果
echo.
echo 💡 如果遇到问题，可以使用以下命令回滚:
echo ssh %USERNAME%@%SERVER_IP% "cp -r %BACKUP_PATH%/* %BLOG_PATH%/ && cd %BLOG_PATH% && npm install && hexo clean && hexo generate && pm2 restart hexo-blog"
echo.
pause

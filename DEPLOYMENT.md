# Xing Blog 部署指南

## 🚀 部署方式选择

### 方式一：静态文件部署（推荐）

#### 前置要求
- VPS服务器（Ubuntu/CentOS等）
- 已安装Nginx
- SSH访问权限

#### 快速部署
```bash
# 1. 给部署脚本执行权限
chmod +x deploy/deploy.sh

# 2. 执行部署（替换为你的服务器IP和用户名）
./deploy/deploy.sh your-server-ip root
```

#### 手动部署步骤
```bash
# 1. 生成静态文件
hexo clean && hexo generate

# 2. 打包文件
tar -czf xing-blog.tar.gz public/

# 3. 上传到服务器
scp xing-blog.tar.gz root@your-server-ip:/tmp/
scp deploy/nginx.conf root@your-server-ip:/tmp/

# 4. 在服务器上执行
ssh root@your-server-ip
sudo mkdir -p /var/www/xing-blog
cd /tmp
tar -xzf xing-blog.tar.gz
sudo cp -r public/* /var/www/xing-blog/
sudo chown -R www-data:www-data /var/www/xing-blog
sudo cp nginx.conf /etc/nginx/sites-available/xing-blog
sudo ln -s /etc/nginx/sites-available/xing-blog /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

### 方式二：Docker部署

#### 前置要求
- 已安装Docker和Docker Compose

#### 部署步骤
```bash
# 1. 构建并启动
docker-compose up -d --build

# 2. 查看状态
docker-compose ps

# 3. 查看日志
docker-compose logs -f xing-blog
```

## 🔧 服务器配置

### Nginx安装（Ubuntu）
```bash
sudo apt update
sudo apt install nginx
sudo systemctl start nginx
sudo systemctl enable nginx
```

### 防火墙配置
```bash
# 开放HTTP端口
sudo ufw allow 80

# 开放HTTPS端口（可选）
sudo ufw allow 443

# 开放SSH端口（确保不被锁定）
sudo ufw allow 22

# 启用防火墙
sudo ufw enable
```

### SSL证书配置（可选）
```bash
# 使用Let's Encrypt
sudo apt install certbot python3-certbot-nginx
sudo certbot --nginx -d your-domain.com
```

## 📝 配置修改

### 域名配置
编辑 `deploy/nginx.conf`：
```nginx
server_name your-domain.com www.your-domain.com;
```

### 路径配置
如需修改网站路径，编辑：
```nginx
root /var/www/xing-blog;  # 修改为你的路径
```

## 🔄 更新部署

### 自动更新
```bash
# 重新运行部署脚本
./deploy/deploy.sh your-server-ip root
```

### 手动更新
```bash
# 1. 重新生成
hexo clean && hexo generate

# 2. 重新部署
# 按照上述手动部署步骤执行
```

## 🐛 故障排除

### 检查Nginx状态
```bash
sudo systemctl status nginx
sudo nginx -t  # 测试配置
```

### 查看错误日志
```bash
sudo tail -f /var/log/nginx/error.log
```

### 检查文件权限
```bash
ls -la /var/www/xing-blog/
sudo chown -R www-data:www-data /var/www/xing-blog
```

### Docker故障排除
```bash
# 查看容器状态
docker ps

# 查看日志
docker logs xing-blog

# 重启容器
docker-compose restart
```

## 📊 性能优化

### 启用Gzip压缩
已在nginx.conf中配置

### 静态文件缓存
已在nginx.conf中配置1年缓存

### CDN配置（可选）
可以将静态资源上传到CDN，修改主题配置中的资源链接

## 🔒 安全建议

1. 定期更新服务器系统
2. 使用非root用户运行服务
3. 配置SSL证书
4. 设置适当的文件权限
5. 定期备份网站数据

## 📞 支持

如遇到问题，请检查：
1. 服务器防火墙设置
2. Nginx配置语法
3. 文件权限设置
4. 域名DNS解析

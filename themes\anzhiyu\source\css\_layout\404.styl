#error-wrap
  display: flex
  justify-content: center
  width: 100%
  margin-top: 1rem
  position: relative
  +maxWidth768()
    margin-top: 0

  .error-content
    box-shadow: none !important
    border-radius: 12px
    background: var(--anzhiyu-card-bg) !important
    display: flex
    flex-direction: row
    justify-content: center
    align-items: center
    margin: 0px 1rem
    height: 22rem
    max-width: 800px
    border-radius: 5px
    background: var(--anzhiyu-card-bg)
    box-shadow: var(--card-box-shadow)
    transition: all 0.3s ease 0s
    border: var(--style-border-always)
    position: relative
    width: 100%
    +maxWidth768()
      -webkit-box-orient: vertical;
      flex-direction: column;
      margin: 0px;
      height: 25rem;
      width: 100%;

    .error-img
      flex: 1 1 0%
      height: 90%
      width: 600px
      border-top-left-radius: 8px
      border-bottom-left-radius: 8px
      background-color: rgb(48, 122, 246)
      background-position: center center
      background-size: cover
      height: 100%;
      +maxWidth768()
        -webkit-box-flex: 1;
        flex: 1 1 0%;
        width: 100%;
        border-radius: 12px;


    .error-info
      flex: 1 1 0%
      padding: 0.5rem
      text-align: center
      font-size: 14px
      font-family: $font-family
      +maxWidth768()
        -webkit-box-flex: 1.1;
        flex: 1.1 1 0%;
        width: 100%;
        padding-bottom: 2rem;

      .error_title
        font-size: 9em
        line-height: 1
        +maxWidth768()
          font-size: 4rem;

      .error_subtitle
        word-break: break-word
        font-size: 1.6em
        -webkit-line-clamp: 2

      a
        display: inline-block
        margin-top: 0.5rem
        padding: 0.3rem 1.5rem
        background: var(--btn-bg)
        color: var(--btn-color)
        i
          padding-right: 0.3rem

.button--animated
  border-radius: 8px !important
  transition: 0.3s
  position: relative
  z-index: 1
  transition: color 1s ease 0s

#body-wrap
  .error-box 
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 1rem;
    padding-top: 0px;
    position: relative;
    .aside-list
      display: flex
      flex-direction: row
      flex-wrap: nowrap
      margin: 1rem
      max-width: 100%
      +maxWidth768()
        margin: 0;

      .aside-list-group
        display: flex
        flex-direction: row
        flex-wrap: wrap
        max-width: 800px
        margin: 0 auto
        justify-content: space-between

      .aside-list-item
        padding: 0.5rem 0
        width: 49%

        .thumbnail
          overflow: hidden
          width: 100%
          height: 200px
          background: var(--anzhiyu-card-bg)
          display: flex
          border-radius: 12px
          +maxWidth768()
            height: 100px;

        img
          width: 100%
          object-fit: cover
          border-radius: 12px
          transition: 0.3s
          transition: filter 300ms ease-in 0.2s, transform 0.6s

        &:hover img
          transform: scale(1.1)
          filter: brightness(0.82)

        .content .title
          -webkit-line-clamp: 2
          overflow: hidden
          display: -webkit-box
          -webkit-box-orient: vertical
          line-height: 1.5
          justify-content: center
          align-items: flex-end
          align-content: center
          padding-top: 0.5rem
          font-size: 16px
          font-weight: bold

        .content time
          display: none

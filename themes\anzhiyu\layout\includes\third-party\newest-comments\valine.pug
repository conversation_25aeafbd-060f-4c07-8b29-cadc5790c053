- let default_avatar = theme.valine.avatar

script(src=url_for(theme.asset.blueimp_md5))
script.
  window.addEventListener('load', () => {
    const changeContent = (content) => {
      if (content === '') return content

      content = content.replace(/<img.*?src="(.*?)"?[^\>]+>/ig, '[!{_p("aside.card_newest_comments.image")}]') // replace image link
      content = content.replace(/<a[^>]+?href=["']?([^"']+)["']?[^>]*>([^<]+)<\/a>/gi, '[!{_p("aside.card_newest_comments.link")}]') // replace url
      content = content.replace(/<pre><code>.*?<\/pre>/gi, '[!{_p("aside.card_newest_comments.code")}]') // replace code
      content = content.replace(/<[^>]+>/g,"") // remove html tag

      if (content.length > 150) {
        content = content.substring(0,150) + '...'
      }
      return content
    }

    const getIcon = (icon, mail) => {
      if (icon) return icon
      let defaultIcon = '!{ default_avatar ? `?d=${default_avatar}` : ''}'
      let iconUrl = `https://gravatar.loli.net/avatar/${md5(mail.toLowerCase()) + defaultIcon}`
      return iconUrl
    }

    const generateHtml = array => {
      let result = ''

      if (array.length) {
        for (let i = 0; i < array.length; i++) {
          result += '<div class=\'aside-list-item\'>'

          if (!{theme.newest_comments.avatar}) {
            const name = '!{theme.lazyload.enable ? "data-lazy-src" : "src"}'
            result += `<a href='${array[i].url}' class='thumbnail'><img ${name}='${array[i].avatar}' alt='${array[i].nick}'></a>`
          }

          result += `<div class='content'>
          <a class='comment' href='${array[i].url}' title='${array[i].content}'>${array[i].content}</a>
          <div class='name'><span>${array[i].nick} / </span><time datetime="${array[i].date}">${anzhiyu.diffDate(array[i].date, true)}</time></div>
          </div></div>`
        }
      } else {
        result += '!{_p("aside.card_newest_comments.zero")}'
      }

      let $dom = document.querySelector('#card-newest-comments .aside-list')
      $dom && ($dom.innerHTML= result)
      window.lazyLoadInstance && window.lazyLoadInstance.update()
      window.pjax && window.pjax.refresh($dom)
    }

    const getComment = () => {
      const serverURL = '!{theme.valine.serverURLs || `https://${theme.valine.appId.substring(0,8)}.api.lncldglobal.com` }'

      var settings = {
        "method": "GET",
        "headers": {
          "X-LC-Id": '!{theme.valine.appId}',
          "X-LC-Key": '!{theme.valine.appKey}',
          "Content-Type": "application/json"
        },
      }

      fetch(`${serverURL}/1.1/classes/Comment?limit=!{theme.newest_comments.limit}&order=-createdAt`,settings)
        .then(response => response.json())
        .then(data => {
          const valineArray = data.results.map(function (e) {
            return {
              'avatar': getIcon(e.QQAvatar, e.mail),
              'content': changeContent(e.comment),
              'nick': e.nick,
              'url': e.url + '#' + e.objectId,
              'date': e.updatedAt,
            }
          })
          saveToLocal.set('valine-newest-comments', JSON.stringify(valineArray), !{theme.newest_comments.storage}/(60*24))
          generateHtml(valineArray)
        }).catch(e => {
          const $dom = document.querySelector('#card-newest-comments .aside-list')
          $dom.textContent= "!{_p('aside.card_newest_comments.error')}"
        }) 
    }

    const newestCommentInit = () => {
      if (document.querySelector('#card-newest-comments .aside-list')) {
        const data = saveToLocal.get('valine-newest-comments')
        if (data) {
          generateHtml(JSON.parse(data))
        } else {
          getComment()
        }
      }
    }

    newestCommentInit()
    document.addEventListener('pjax:complete', newestCommentInit)
  })
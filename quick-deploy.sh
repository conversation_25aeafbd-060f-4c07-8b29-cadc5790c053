#!/bin/bash

# Xing Blog 快速部署脚本
# 使用方法: ./quick-deploy.sh [选项]
# 选项: --preview (仅预览不部署)

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# 配置变量
SERVER_IP="************"
USERNAME="root"
REMOTE_PATH="/var/www/xing-blog"
LOCAL_PORT="4000"

# 显示横幅
show_banner() {
    echo -e "${PURPLE}"
    echo "  ╔══════════════════════════════════════╗"
    echo "  ║           Xing Blog 部署工具          ║"
    echo "  ║        快速预览 & 一键部署            ║"
    echo "  ╚══════════════════════════════════════╝"
    echo -e "${NC}"
}

# 检查依赖
check_dependencies() {
    echo -e "${BLUE}🔍 检查环境依赖...${NC}"
    
    if ! command -v node &> /dev/null; then
        echo -e "${RED}❌ Node.js 未安装，请先安装 Node.js${NC}"
        exit 1
    fi
    
    if ! command -v hexo &> /dev/null; then
        echo -e "${YELLOW}⚠️  Hexo CLI 未安装，正在安装...${NC}"
        npm install -g hexo-cli
    fi
    
    if [ ! -f "package.json" ]; then
        echo -e "${RED}❌ 请在博客根目录下运行此脚本${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}✅ 环境检查通过${NC}"
}

# 安装依赖
install_dependencies() {
    if [ ! -d "node_modules" ]; then
        echo -e "${BLUE}📦 安装项目依赖...${NC}"
        npm install
    fi
}

# 本地预览
local_preview() {
    echo -e "${BLUE}🚀 启动本地预览服务器...${NC}"
    echo -e "${YELLOW}📝 预览地址: http://localhost:${LOCAL_PORT}${NC}"
    echo -e "${YELLOW}💡 按 Ctrl+C 停止预览服务器${NC}"
    echo -e "${YELLOW}🔄 文件修改后会自动刷新页面${NC}"
    echo ""
    
    # 检查端口是否被占用
    if lsof -Pi :${LOCAL_PORT} -sTCP:LISTEN -t >/dev/null 2>&1; then
        echo -e "${YELLOW}⚠️  端口 ${LOCAL_PORT} 已被占用，尝试使用其他端口...${NC}"
        LOCAL_PORT=$((LOCAL_PORT + 1))
    fi
    
    # 启动预览服务器
    hexo server -p ${LOCAL_PORT}
}

# 生成静态文件
generate_static() {
    echo -e "${BLUE}🔨 清理缓存并生成静态文件...${NC}"
    
    # 清理旧文件
    hexo clean
    
    # 生成新文件
    hexo generate
    
    # 检查生成结果
    if [ ! -d "public" ] || [ -z "$(ls -A public)" ]; then
        echo -e "${RED}❌ 静态文件生成失败${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}✅ 静态文件生成完成${NC}"
    
    # 显示生成的文件统计
    FILE_COUNT=$(find public -type f | wc -l)
    TOTAL_SIZE=$(du -sh public | cut -f1)
    echo -e "${BLUE}📊 生成统计: ${FILE_COUNT} 个文件，总大小 ${TOTAL_SIZE}${NC}"
}

# 部署前检查
pre_deploy_check() {
    echo -e "${BLUE}🔍 部署前检查...${NC}"
    
    # 检查关键文件
    if [ ! -f "public/index.html" ]; then
        echo -e "${RED}❌ 缺少首页文件${NC}"
        exit 1
    fi
    
    # 检查SSH连接
    echo -e "${BLUE}🔗 测试服务器连接...${NC}"
    if ! ssh -o ConnectTimeout=10 ${USERNAME}@${SERVER_IP} "echo 'SSH连接成功'" 2>/dev/null; then
        echo -e "${RED}❌ 无法连接到服务器 ${SERVER_IP}${NC}"
        echo -e "${YELLOW}💡 请检查：${NC}"
        echo -e "   1. 服务器IP地址是否正确"
        echo -e "   2. SSH密钥是否配置正确"
        echo -e "   3. 网络连接是否正常"
        exit 1
    fi
    
    echo -e "${GREEN}✅ 部署前检查通过${NC}"
}

# 部署到服务器
deploy_to_server() {
    echo -e "${BLUE}🚀 开始部署到服务器...${NC}"
    
    # 创建时间戳
    TIMESTAMP=$(date +%Y%m%d-%H%M%S)
    PACKAGE_NAME="xing-blog-${TIMESTAMP}.tar.gz"
    
    # 打包文件
    echo -e "${BLUE}📦 打包静态文件...${NC}"
    tar -czf ${PACKAGE_NAME} -C public .
    
    # 上传文件
    echo -e "${BLUE}📤 上传文件到服务器...${NC}"
    scp ${PACKAGE_NAME} ${USERNAME}@${SERVER_IP}:/tmp/
    
    # 在服务器上部署
    echo -e "${BLUE}🔧 在服务器上部署...${NC}"
    ssh ${USERNAME}@${SERVER_IP} << EOF
        set -e
        
        echo "创建备份..."
        if [ -d "${REMOTE_PATH}" ] && [ "\$(ls -A ${REMOTE_PATH} 2>/dev/null)" ]; then
            sudo mkdir -p ${REMOTE_PATH}/backup
            sudo cp -r ${REMOTE_PATH}/* ${REMOTE_PATH}/backup/ 2>/dev/null || true
        fi
        
        echo "部署新版本..."
        cd /tmp
        sudo mkdir -p ${REMOTE_PATH}
        sudo rm -rf ${REMOTE_PATH}/*
        sudo tar -xzf ${PACKAGE_NAME} -C ${REMOTE_PATH}
        
        echo "设置权限..."
        sudo chown -R www-data:www-data ${REMOTE_PATH}
        sudo chmod -R 755 ${REMOTE_PATH}
        
        echo "重新加载Nginx..."
        sudo nginx -t && sudo systemctl reload nginx
        
        echo "清理临时文件..."
        rm -f ${PACKAGE_NAME}
        
        echo "部署完成！"
EOF
    
    # 清理本地临时文件
    rm -f ${PACKAGE_NAME}
    
    echo -e "${GREEN}🎉 部署完成！${NC}"
    echo -e "${BLUE}🌐 访问地址: https://an.xing2006.me${NC}"
}

# 显示使用帮助
show_help() {
    echo -e "${BLUE}使用方法:${NC}"
    echo -e "  ./quick-deploy.sh [选项]"
    echo ""
    echo -e "${BLUE}选项:${NC}"
    echo -e "  --preview, -p    仅启动本地预览，不部署"
    echo -e "  --deploy, -d     直接部署，跳过预览"
    echo -e "  --help, -h       显示此帮助信息"
    echo ""
    echo -e "${BLUE}示例:${NC}"
    echo -e "  ./quick-deploy.sh           # 交互式模式"
    echo -e "  ./quick-deploy.sh --preview # 仅预览"
    echo -e "  ./quick-deploy.sh --deploy  # 直接部署"
}

# 交互式菜单
interactive_menu() {
    while true; do
        echo ""
        echo -e "${BLUE}请选择操作:${NC}"
        echo -e "  ${GREEN}1)${NC} 本地预览"
        echo -e "  ${GREEN}2)${NC} 生成并部署到服务器"
        echo -e "  ${GREEN}3)${NC} 仅生成静态文件"
        echo -e "  ${GREEN}4)${NC} 退出"
        echo ""
        read -p "请输入选项 (1-4): " choice
        
        case $choice in
            1)
                generate_static
                local_preview
                ;;
            2)
                generate_static
                pre_deploy_check
                
                echo ""
                echo -e "${YELLOW}⚠️  即将部署到生产服务器！${NC}"
                read -p "确认部署? (y/N): " -n 1 -r
                echo
                if [[ $REPLY =~ ^[Yy]$ ]]; then
                    deploy_to_server
                else
                    echo -e "${YELLOW}⏹️  部署已取消${NC}"
                fi
                ;;
            3)
                generate_static
                echo -e "${GREEN}✅ 静态文件已生成到 public/ 目录${NC}"
                ;;
            4)
                echo -e "${BLUE}👋 再见！${NC}"
                exit 0
                ;;
            *)
                echo -e "${RED}❌ 无效选项，请重新选择${NC}"
                ;;
        esac
    done
}

# 主函数
main() {
    show_banner
    check_dependencies
    install_dependencies
    
    # 解析命令行参数
    case "${1:-}" in
        --preview|-p)
            generate_static
            local_preview
            ;;
        --deploy|-d)
            generate_static
            pre_deploy_check
            deploy_to_server
            ;;
        --help|-h)
            show_help
            ;;
        "")
            interactive_menu
            ;;
        *)
            echo -e "${RED}❌ 未知选项: $1${NC}"
            show_help
            exit 1
            ;;
    esac
}

# 捕获中断信号
trap 'echo -e "\n${YELLOW}⏹️  操作已中断${NC}"; exit 130' INT

# 运行主函数
main "$@"

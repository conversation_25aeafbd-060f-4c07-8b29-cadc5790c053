- let tocNumber = page.toc_number !== undefined ? page.toc_number : theme.toc.number
- let tocExpand = page.toc_expand !== undefined ? page.toc_expand : theme.toc.expand
- let tocExpandClass = tocExpand ? 'is-expand' : ''

#card-toc.card-widget
  .item-headline
    i.anzhiyufont.anzhiyu-icon-bars
    span= _p('aside.card_toc')
    span.toc-percentage

  if (page.encrypt == true)
    .toc-content.toc-div-class(class=tocExpandClass style="display:none")!=toc(page.origin, {list_number: tocNumber})
  else 
    .toc-content(class=tocExpandClass)!=toc(page.content, {list_number: tocNumber})
      
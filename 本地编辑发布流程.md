# Xing Blog 本地编辑与发布流程指南

## 📝 概述

本文档详细介绍如何在本地环境中编辑博客内容，预览效果，并将满意的内容发布到VPS服务器的完整流程。

## 🛠️ 环境准备

### 必需软件
- **Node.js** (版本 14 或更高)
- **Git** (用于版本控制)
- **文本编辑器** (推荐 VS Code)
- **SSH客户端** (用于连接VPS)

### 项目结构
```
anzhiyu-blog/
├── _config.yml              # 博客主配置文件
├── _config.anzhiyu.yml      # 主题配置文件
├── source/                  # 源文件目录
│   ├── _posts/             # 文章目录
│   ├── _data/              # 数据文件
│   ├── json/               # JSON数据文件
│   └── images/             # 图片资源
├── themes/                  # 主题目录
├── public/                  # 生成的静态文件
└── package.json            # 项目依赖
```

## ✍️ 本地编辑流程

### 1. 启动本地开发环境

```bash
# 进入项目目录
cd anzhiyu-blog

# 安装依赖（首次运行）
npm install

# 启动本地服务器
hexo server
# 或指定端口
hexo server -p 4000
```

访问地址：`http://localhost:4000`

### 2. 创建新文章

```bash
# 创建新文章
hexo new "文章标题"

# 创建草稿
hexo new draft "草稿标题"

# 发布草稿
hexo publish "草稿标题"
```

### 3. 文章编辑

#### 文章头部信息 (Front Matter)
```yaml
---
title: 文章标题
date: 2025-08-14 10:00:00
updated: 2025-08-14 10:00:00
tags: 
  - 标签1
  - 标签2
categories: 
  - 分类1
  - 子分类
description: 文章描述
cover: /images/cover.jpg
top_img: /images/top.jpg
---
```

#### 常用标签插件
```markdown
<!-- 提示框 -->
{% note success %}
成功提示内容
{% endnote %}

<!-- 代码块 -->
{% codeblock lang:javascript %}
console.log('Hello World');
{% endcodeblock %}

<!-- 图片 -->
{% asset_img example.jpg 图片描述 %}

<!-- 链接卡片 -->
{% link 标题,链接,描述 %}
```

### 4. 实时预览

本地服务器支持热重载，修改文件后会自动刷新浏览器显示最新内容。

#### 预览检查清单
- [ ] 文章标题和内容显示正确
- [ ] 图片加载正常
- [ ] 代码高亮效果
- [ ] 标签和分类正确
- [ ] 响应式布局在不同设备上的表现
- [ ] 音乐播放器功能
- [ ] 评论系统（如果启用）

## 🎨 主题和样式自定义

### 修改主题配置
```bash
# 编辑主题配置文件
nano _config.anzhiyu.yml
```

### 自定义CSS
```bash
# 创建自定义样式文件
mkdir -p source/css/custom
echo "/* 自定义样式 */" > source/css/custom/custom.css
```

### 修改音乐库
```bash
# 编辑音乐列表
nano source/json/music.json
```

## 🚀 发布到VPS流程

### 方法一：手动发布（推荐）

#### 1. 生成静态文件
```bash
# 清理缓存并生成
hexo clean && hexo generate

# 检查生成的文件
ls -la public/
```

#### 2. 打包文件
```bash
# 创建压缩包
tar -czf blog-$(date +%Y%m%d-%H%M%S).tar.gz public/

# 或使用zip
zip -r blog-$(date +%Y%m%d-%H%M%S).zip public/
```

#### 3. 上传到VPS
```bash
# 上传压缩包
scp blog-*.tar.gz root@************:/tmp/

# 连接到VPS
ssh root@************

# 在VPS上执行
cd /tmp
tar -xzf blog-*.tar.gz
sudo rm -rf /var/www/xing-blog/*
sudo cp -r public/* /var/www/xing-blog/
sudo chown -R www-data:www-data /var/www/xing-blog
sudo chmod -R 755 /var/www/xing-blog
```

### 方法二：使用部署脚本

#### 1. 创建部署脚本
```bash
# 创建部署脚本
cat > deploy.sh << 'EOF'
#!/bin/bash
echo "🚀 开始部署博客..."

# 生成静态文件
echo "📦 生成静态文件..."
hexo clean && hexo generate

# 创建时间戳
TIMESTAMP=$(date +%Y%m%d-%H%M%S)

# 打包文件
echo "📦 打包文件..."
tar -czf blog-${TIMESTAMP}.tar.gz public/

# 上传到服务器
echo "📤 上传到服务器..."
scp blog-${TIMESTAMP}.tar.gz root@************:/tmp/

# 在服务器上部署
echo "🔧 在服务器上部署..."
ssh root@************ << REMOTE_EOF
cd /tmp
tar -xzf blog-${TIMESTAMP}.tar.gz
sudo cp -r public/* /var/www/xing-blog/
sudo chown -R www-data:www-data /var/www/xing-blog
sudo chmod -R 755 /var/www/xing-blog
rm -f blog-${TIMESTAMP}.tar.gz
REMOTE_EOF

# 清理本地文件
rm -f blog-${TIMESTAMP}.tar.gz

echo "✅ 部署完成！"
echo "🌐 访问地址: https://an.xing2006.me"
EOF

# 给脚本执行权限
chmod +x deploy.sh
```

#### 2. 执行部署
```bash
./deploy.sh
```

### 方法三：Git同步（高级）

#### 1. 初始化Git仓库
```bash
# 初始化仓库
git init
git add .
git commit -m "Initial commit"

# 添加远程仓库（可选）
git remote add origin https://github.com/yourusername/your-blog.git
```

#### 2. 在VPS上设置Git Hook
```bash
# 在VPS上创建裸仓库
ssh root@************
git init --bare /var/git/blog.git

# 创建post-receive hook
cat > /var/git/blog.git/hooks/post-receive << 'EOF'
#!/bin/bash
cd /tmp
git clone /var/git/blog.git blog-temp
cd blog-temp
npm install
hexo clean && hexo generate
sudo cp -r public/* /var/www/xing-blog/
sudo chown -R www-data:www-data /var/www/xing-blog
cd ..
rm -rf blog-temp
EOF

chmod +x /var/git/blog.git/hooks/post-receive
```

#### 3. 推送部署
```bash
# 添加VPS作为远程仓库
git remote add vps root@************:/var/git/blog.git

# 推送部署
git push vps main
```

## 📋 日常工作流程

### 1. 开始写作
```bash
# 启动本地服务器
hexo server -p 4000

# 创建新文章
hexo new "今日学习笔记"
```

### 2. 编辑和预览
- 在 `source/_posts/` 目录下编辑文章
- 浏览器访问 `http://localhost:4000` 预览
- 实时查看修改效果

### 3. 发布前检查
- [ ] 文章内容无误
- [ ] 图片显示正常
- [ ] 标签分类正确
- [ ] 移动端适配良好
- [ ] 加载速度可接受

### 4. 发布到线上
```bash
# 使用部署脚本
./deploy.sh

# 或手动部署
hexo clean && hexo generate
# 然后上传 public/ 目录内容
```

## 🔧 常用命令速查

### Hexo命令
```bash
hexo new [layout] <title>    # 创建新文章
hexo generate               # 生成静态文件
hexo server                 # 启动本地服务器
hexo deploy                 # 部署到远程
hexo clean                  # 清理缓存文件
hexo list <type>            # 列出网站资料
```

### 文件操作
```bash
# 查看文章列表
ls source/_posts/

# 编辑配置文件
nano _config.anzhiyu.yml

# 查看生成的文件
ls public/
```

### 服务器操作
```bash
# 连接VPS
ssh root@************

# 查看网站文件
ls -la /var/www/xing-blog/

# 查看Nginx日志
sudo tail -f /var/log/nginx/xing-blog.access.log

# 重启Nginx
sudo systemctl reload nginx
```

## 🐛 常见问题解决

### 1. 本地服务器启动失败
```bash
# 检查端口占用
netstat -tlnp | grep :4000

# 更换端口
hexo server -p 4001
```

### 2. 生成文件失败
```bash
# 清理缓存重新生成
hexo clean
rm -rf .deploy_git/
hexo generate
```

### 3. 图片不显示
- 检查图片路径是否正确
- 确保图片文件存在于 `source/images/` 目录
- 使用相对路径引用图片

### 4. 上传到VPS失败
```bash
# 检查SSH连接
ssh root@************

# 检查文件权限
ls -la /var/www/xing-blog/

# 修复权限
sudo chown -R www-data:www-data /var/www/xing-blog
sudo chmod -R 755 /var/www/xing-blog
```

## 📚 进阶技巧

### 1. 使用草稿功能
```bash
# 创建草稿
hexo new draft "未完成的文章"

# 预览草稿
hexo server --draft

# 发布草稿
hexo publish "未完成的文章"
```

### 2. 批量处理图片
```bash
# 压缩图片（需要安装imagemin）
npm install -g imagemin-cli
imagemin source/images/* --out-dir=source/images/compressed/
```

### 3. 自动化备份
```bash
# 创建备份脚本
cat > backup.sh << 'EOF'
#!/bin/bash
DATE=$(date +%Y%m%d)
tar -czf backup-${DATE}.tar.gz source/ _config*.yml themes/
echo "备份完成: backup-${DATE}.tar.gz"
EOF

chmod +x backup.sh
```

## 🎯 最佳实践

1. **定期备份**：定期备份源文件和配置
2. **版本控制**：使用Git管理文章版本
3. **图片优化**：压缩图片减少加载时间
4. **SEO优化**：合理使用标题、描述和标签
5. **移动适配**：确保在移动设备上显示良好
6. **性能监控**：定期检查网站加载速度

---

## 📞 支持与帮助

如果在使用过程中遇到问题：
1. 查看Hexo官方文档：https://hexo.io/docs/
2. 查看安知鱼主题文档：https://docs.anheyu.com/
3. 检查本地和服务器的错误日志

**祝你写作愉快！** 🎉

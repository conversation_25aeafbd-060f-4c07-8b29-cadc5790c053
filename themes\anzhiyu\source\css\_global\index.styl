:root
  --animation-on: cubic-bezier(.6, .1, 0, 1);
  --animation-in: cubic-bezier(.6, .2, .25, 1);
  --animation-ot: opacity .5s var(--animation-in) backwards, transform 1s var(--animation-in) backwards;
  --animation-otf: opacity .5s var(--animation-in) backwards, transform 1s var(--animation-in) backwards, filter .7s var(--animation-in) backwards;
  --global-font-size: $font-size
  --global-bg: $body-bg
  --font-color: $font-black
  --hr-border: lighten($theme-hr-color, 50%)
  --hr-before-color: lighten($theme-hr-color, 30%)
  --search-bg: $search-bg
  --search-input-color: $search-input-color
  --search-result-title: $search-result-title
  --preloader-bg: $preloader-bg
  --preloader-color: $preloader-word-color
  --tab-border-color: $tab-border-color
  --tab-botton-bg: $tab-botton-bg
  --tab-botton-color: $tab-botton-color
  --tab-button-hover-bg: $tab-button-hover-bg
  --tab-button-active-bg: $tab-button-active-bg
  --card-bg: $card-bg
  --sidebar-bg: $sidebar-background
  --btn-hover-color: $button-hover-color
  --btn-color: $button-color
  --btn-bg: $button-bg
  --text-bg-hover: rgba($text-bg-hover, .7)
  --light-grey: $light-grey
  --dark-grey: $dark-grey
  --white: $white
  --text-highlight-color: $text-highlight-color
  --blockquote-color: $blockquote-color
  --blockquote-bg: $blockquote-background-color
  --reward-pop: $reward-pop-up-bg
  --toc-link-color: $toc-link-color
  --card-box-shadow: 0 3px 8px 6px rgba(7, 17, 27, .05)
  --card-hover-box-shadow: 0 3px 8px 6px rgba(7, 17, 27, .09)
  --pseudo-hover: $pseudo-hover
  --headline-presudo: var(--anzhiyu-fontcolor)
  --scrollbar-color: $scrollbar-color
  --anzhiyu-snackbar-time: 2000ms;
  --anzhiyu-deep-purple: #405589;
  --anzhiyu-meta-theme-color: #f7f9fe;
  --anzhiyu-bar-background: var(--anzhiyu-main);
  --anzhiyu-theme-op: #4259ef23;
  --anzhiyu-card-bg-none: rgba(255, 255, 255, 0);
  --anzhiyu-main-op-deep: var(--anzhiyu-theme-op-deep) !important;
  --anzhiyu-gray-op: #9999992b;
  --anzhiyu-theme-top: var(--anzhiyu-theme);
  --anzhiyu-white: #fff;
  --anzhiyu-white-op: rgba(255, 255, 255, 0.2);
  --anzhiyu-black: #000;
  --anzhiyu-black-op: rgba(0, 0, 0, 0.2);
  --anzhiyu-none: rgba(0, 0, 0, 0);
  --anzhiyu-gray: #999999;
  --anzhiyu-yellow: #ffc93e;
  --anzhiyu-orange: #e38100;
  --anzhiyu-blue-hover: #5390e6;
  --anzhiyu-radius-full: 50px;
  --anzhiyu-radius: 8px;
  --anzhiyu-border-radius: 8px;
  --anzhiyu-main: var(--anzhiyu-theme);
  --anzhiyu-main-op: var(--anzhiyu-theme-op);
  --anzhiyu-shadow-theme: 0 8px 12px -3px var(--anzhiyu-theme-op);
  --anzhiyu-shadow-main: 0 8px 12px -3px var(--anzhiyu-main-op);
  --anzhiyu-shadow-blue: 0 8px 12px -3px rgba(40, 109, 234, 0.2);
  --anzhiyu-shadow-white: 0 8px 12px -3px rgba(255, 255, 255, 0.2);
  --anzhiyu-shadow-black: 0 0 12px 4px rgba(0, 0, 0, 0.05);
  --anzhiyu-shadow-yellow: 0px 38px 77px -26px rgba(255, 201, 62, 0.12);
  --anzhiyu-shadow-red: 0 8px 12px -3px #ee7d7936;
  --anzhiyu-shadow-green: 0 8px 12px -3px #87ee7936;
  --anzhiyu-shadow-border: 0 8px 16px -4px #2c2d300c;
  --anzhiyu-shadow-blackdeep: 0 2px 16px -3px rgba(0, 0, 0, 0.15);
  --anzhiyu-logo-color: linear-gradient(215deg, #4584ff 0%, #cf0db9 100%);
  --anzhiyu-code-stress: var(--anzhiyu-main);
  --style-border: 1px solid var(--anzhiyu-card-border);
  --anzhiyu-blue-main: #425AEF;
  --style-border-hover: 1px solid var(--anzhiyu-main);
  --style-border-dashed: 1px dashed var(--anzhiyu-theme-op);
  --style-border-avatar: 5px solid var(--anzhiyu-white);
  --style-border-always: 1px solid var(--anzhiyu-card-border);
  --style-border-none: 1px solid transparent;
  --style-border-deep-ash: 1px solid #d0d7de;
  --style-border-hover-always: 1px solid var(--anzhiyu-main);
  --anzhiyu-main-none: #b8b8b800 !important;
  --anzhiyu-wihite-font: #fff

+schemeLight()
  --anzhiyu-meta-theme-post-color: #fff;
  --anzhiyu-meta-theme-color: #f7f9fe;
  --anzhiyu-theme-op-deep: #4259efdd;
  --global-bg: #f7f9fe;
  --anzhiyu-theme: $theme-color;
  --anzhiyu-theme-deep: #1856fb;
  --anzhiyu-theme-op: #4259ef23;
  --anzhiyu-blue: #5ca1ff;
  --anzhiyu-blue-tint: rgba(92, 161, 255, 0.1);
  --anzhiyu-red: #d8213c;
  --anzhiyu-pink: #ff7c7c;
  --anzhiyu-green: #57bd6a;
  --anzhiyu-fontcolor: #363636;
  --anzhiyu-background: #f7f9fe;
  --anzhiyu-reverse: #000;
  --anzhiyu-maskbg: rgba(255, 255, 255, 0.6);
  --anzhiyu-maskbgdeep: rgba(255, 255, 255, 0.85);
  --anzhiyu-scrollbar: rgba(60, 60, 67, 0.4);
  --anzhiyu-hovertext: var(--anzhiyu-theme);
  --anzhiyu-ahoverbg: #f7f7fa;
  --anzhiyu-lighttext: var(--anzhiyu-main);
  --anzhiyu-secondtext: rgba(60, 60, 67, 0.8);
  --anzhiyu-scrollbar: rgba(60, 60, 67, 0.4);
  --anzhiyu-card-btn-bg: #edf0f7;
  --anzhiyu-post-blockquote-bg: #fafcff;
  --anzhiyu-post-tabs-bg: #f2f5f8;
  --anzhiyu-secondbg: #f7f7f9;
  --anzhiyu-shadow-nav: 0 5px 12px -5px rgba(102, 68, 68, 0.05);
  --anzhiyu-card-bg: #fff;
  --anzhiyu-shadow-lightblack: 0 5px 12px -5px rgba(102, 68, 68, 0);
  --anzhiyu-shadow-light2black: 0 5px 12px -5px rgba(102, 68, 68, 0);
  --anzhiyu-card-border: #e3e8f7;
  --anzhiyu-shadow-lightblack: 0 5px 12px -5px rgba(102, 68, 68, 0.00);
  --anzhiyu-shadow-light2black: 0 5px 12px -5px rgba(102, 68, 68, 0.00);
  --anzhiyu-wihite-font: #fff

[data-theme="light"]
  --anzhiyu-meta-theme-post-color: #fff;
  --anzhiyu-meta-theme-color: #f7f9fe;
  --anzhiyu-theme-op-deep: #4259efdd;
  --global-bg: #f7f9fe;
  --anzhiyu-theme: $theme-color;
  --anzhiyu-theme-deep: #1856fb;
  --anzhiyu-theme-op: #4259ef23;
  --anzhiyu-blue: #5ca1ff;
  --anzhiyu-blue-tint: rgba(92, 161, 255, 0.1);
  --anzhiyu-red: #d8213c;
  --anzhiyu-pink: #ff7c7c;
  --anzhiyu-green: #57bd6a;
  --anzhiyu-fontcolor: #363636;
  --anzhiyu-background: #f7f9fe;
  --anzhiyu-reverse: #000;
  --anzhiyu-maskbg: rgba(255, 255, 255, 0.6);
  --anzhiyu-maskbgdeep: rgba(255, 255, 255, 0.85);
  --anzhiyu-scrollbar: rgba(60, 60, 67, 0.4);
  --anzhiyu-hovertext: var(--anzhiyu-theme);
  --anzhiyu-ahoverbg: #f7f7fa;
  --anzhiyu-lighttext: var(--anzhiyu-main);
  --anzhiyu-secondtext: rgba(60, 60, 67, 0.8);
  --anzhiyu-scrollbar: rgba(60, 60, 67, 0.4);
  --anzhiyu-card-btn-bg: #edf0f7;
  --anzhiyu-post-blockquote-bg: #fafcff;
  --anzhiyu-post-tabs-bg: #f2f5f8;
  --anzhiyu-secondbg: #f7f7f9;
  --anzhiyu-shadow-nav: 0 5px 12px -5px rgba(102, 68, 68, 0.05);
  --anzhiyu-card-bg: #fff;
  --anzhiyu-shadow-lightblack: 0 5px 12px -5px rgba(102, 68, 68, 0);
  --anzhiyu-shadow-light2black: 0 5px 12px -5px rgba(102, 68, 68, 0);
  --anzhiyu-card-border: #e3e8f7;
  --anzhiyu-shadow-lightblack: 0 5px 12px -5px rgba(102, 68, 68, 0.00);
  --anzhiyu-shadow-light2black: 0 5px 12px -5px rgba(102, 68, 68, 0.00);
  --anzhiyu-wihite-font: #fff

+schemeDark()
  --anzhiyu-meta-theme-post-color: #1d1e22;
  --anzhiyu-meta-theme-color: #000;
  --anzhiyu-blue: #5ca1ff;
  --anzhiyu-blue-tint: rgba(92, 161, 255, 0.1);
  --anzhiyu-theme-op-deep: #0084ffdd;
  --global-bg: #18171d;
  --anzhiyu-theme: $theme-dark-color;
  --anzhiyu-theme-deep: #0076e5;
  --anzhiyu-theme-op: #f2b94b23;
  --anzhiyu-blue: #0084ff;
  --anzhiyu-red: #ff3842;
  --anzhiyu-pink: #ff7c7c;
  --anzhiyu-green: #57bd6a;
  --anzhiyu-fontcolor: #f7f7fa;
  --anzhiyu-background: #18171d;
  --anzhiyu-reverse: #fff;
  --anzhiyu-maskbg: rgba(0, 0, 0, 0.6);
  --anzhiyu-maskbgdeep: rgba(0, 0, 0, 0.85);
  --anzhiyu-hovertext: #0a84ff;
  --anzhiyu-ahoverbg: #fff;
  --anzhiyu-scrollbar: rgba(200, 200, 223, 0.4);
  --anzhiyu-lighttext: #f2b94b;
  --anzhiyu-secondtext: #a1a2b8;
  --anzhiyu-scrollbar: rgba(200, 200, 223, 0.4);
  --anzhiyu-card-btn-bg: #30343f;
  --anzhiyu-post-blockquote-bg: #000;
  --anzhiyu-post-tabs-bg: #121212;
  --anzhiyu-secondbg: #21232a;
  --anzhiyu-shadow-nav: 0 5px 20px 0px rgba(28, 28, 28, 0.4);
  --anzhiyu-card-bg: #1d1e22;
  --anzhiyu-shadow-lightblack: 0 5px 12px -5px rgba(102, 68, 68, 0);
  --anzhiyu-shadow-light2black: 0 5px 12px -5px rgba(102, 68, 68, 0);
  --anzhiyu-card-border: #42444a;
  --anzhiyu-shadow-lightblack: 0 5px 12px -5px rgba(102, 68, 68, 0.0);
  --anzhiyu-shadow-light2black: 0 5px 12px -5px rgba(102, 68, 68, 0.0);
  --anzhiyu-wihite-font: #f6f6f6

[data-theme="dark"]
  --anzhiyu-meta-theme-post-color: #1d1e22;
  --anzhiyu-meta-theme-color: #000;
  --anzhiyu-blue: #5ca1ff;
  --anzhiyu-blue-tint: rgba(92, 161, 255, 0.1);
  --anzhiyu-theme-op-deep: #0084ffdd;
  --global-bg: #18171d;
  --anzhiyu-theme: $theme-dark-color;
  --anzhiyu-theme-deep: #0076e5;
  --anzhiyu-theme-op: #f2b94b23;
  --anzhiyu-blue: #0084ff;
  --anzhiyu-red: #ff3842;
  --anzhiyu-pink: #ff7c7c;
  --anzhiyu-green: #57bd6a;
  --anzhiyu-fontcolor: #f7f7fa;
  --anzhiyu-background: #18171d;
  --anzhiyu-reverse: #fff;
  --anzhiyu-maskbg: rgba(0, 0, 0, 0.6);
  --anzhiyu-maskbgdeep: rgba(0, 0, 0, 0.85);
  --anzhiyu-hovertext: #0a84ff;
  --anzhiyu-ahoverbg: #fff;
  --anzhiyu-scrollbar: rgba(200, 200, 223, 0.4);
  --anzhiyu-lighttext: #f2b94b;
  --anzhiyu-secondtext: #a1a2b8;
  --anzhiyu-scrollbar: rgba(200, 200, 223, 0.4);
  --anzhiyu-card-btn-bg: #30343f;
  --anzhiyu-post-blockquote-bg: #000;
  --anzhiyu-post-tabs-bg: #121212;
  --anzhiyu-secondbg: #21232a;
  --anzhiyu-shadow-nav: 0 5px 20px 0px rgba(28, 28, 28, 0.4);
  --anzhiyu-card-bg: #1d1e22;
  --anzhiyu-shadow-lightblack: 0 5px 12px -5px rgba(102, 68, 68, 0);
  --anzhiyu-shadow-light2black: 0 5px 12px -5px rgba(102, 68, 68, 0);
  --anzhiyu-card-border: #42444a;
  --anzhiyu-shadow-lightblack: 0 5px 12px -5px rgba(102, 68, 68, 0.0);
  --anzhiyu-shadow-light2black: 0 5px 12px -5px rgba(102, 68, 68, 0.0);
  --anzhiyu-wihite-font: #f6f6f6

body
  position: relative
  min-height: 100%
  background: var(--global-bg)
  color: var(--font-color)
  font-size: var(--global-font-size)
  font-family: $font-family
  line-height: $text-line-height
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0)

  if !hexo-config('copy.enable')
    user-select: none

// scrollbar - chrome/safari
*::-webkit-scrollbar
  width: 6px
  height: 6px

*::-webkit-scrollbar-thumb
  background: var(--scrollbar-color)
  border-radius: 8px;
  cursor: pointer;

*::-webkit-scrollbar-track
  background-color: transparent

::-webkit-scrollbar-corner
  background-color: transparent;


// scrollbar - firefox
*
  scrollbar-width: thin
  scrollbar-color: var(--scrollbar-color) transparent

input::placeholder
  color: var(--font-color)

#web_bg
  position: fixed
  z-index: -999
  width: 200%;
  height: 200%;
  top: -50%;
  left: -50%;
  transform: rotate(-1deg);
  -webkit-overflow-scrolling: touch;
  background: var(--anzhiyu-background)
  background-attachment: local
  background-position: center
  background-size: cover
  background-repeat: no-repeat

h1,
h2,
h3,
h4,
h5,
h6
  position: relative
  margin: 20px 0 14px
  color: var(--text-highlight-color)
  font-weight: bold

  code
    font-size: inherit !important

*
  box-sizing: border-box

hr
  position: relative
  margin: 40px auto
  border: 2px dashed var(--hr-border)

  if hexo-config('hr_icon.enable')
    width: calc(100% - 4px)

    &:hover
      &:before
        left: calc(95% - 20px)

    &:before
      position: absolute
      top: $hr-icon-top
      left: 5%
      z-index: 1
      color: var(--hr-before-color)
      content: $hr-icon
      font-size: 16px
      line-height: 1
      transition: all 1s ease-in-out
      if hexo-config('icons.fontawesome')
        @extend .fontawesomeIcon
      else
        @extend .anzhiyufont

html
  overflow-y: overlay;
.table-wrap
  overflow-x: scroll
  margin: 1rem 0;
  border-radius: 8px;

table
  display: table
  width: 100%
  border-spacing: 0
  border-collapse: collapse
  empty-cells: show
  if hexo-config('table_interlaced_discoloration')
    tr:nth-child(even)
      background-color: var(--anzhiyu-secondbg);
  thead
    background: var(--anzhiyu-secondbg);

  th,
  td
    padding: 0.3rem 0.6rem;
    vertical-align: middle
    border: var(--style-border-always);

*::selection
  background: $theme-text-selection-color
  color: #F7F7F7

button
  padding: 0
  outline: 0
  border: none
  background: none
  cursor: pointer
  touch-action: manipulation

a
  color: $a-link-color
  text-decoration: none
  word-wrap: break-word
  transition: all 0.2s ease 0s;
  overflow-wrap: break-word

  &:hover
    color: $light-blue

// font
if $site-name-font
  #site-title,
  #site-subtitle,
  #site-name,
  #aside-content .author-info__name,
  #aside-content .author-info__description
    font-family: $site-name-font

.is-center
  text-align: center

.copy-true
  user-select: all

.pull-left
  float: left

.pull-right
  float: right

img
  &[src=''],
  &:not([src])
    opacity: 0

// lazyload blur
if hexo-config('lazyload.enable') && hexo-config('lazyload.blur') && !hexo-config('lazyload.placeholder')
  img
    &[data-lazy-src]:not(.loaded)
      filter: blur(8px) brightness(1)

    &[data-lazy-src].error
      filter: none
      opacity 1 !important

// lazyload progressive
if hexo-config('lazyload.enable') && hexo-config('lazyload.progressive') && !hexo-config('lazyload.placeholder')
  img
    &[data-lazy-src]:not(.loaded)
      opacity: 0;
  img[src=''], img:not([src]) 
    opacity 0
.img-alt
  font-size: 12px;
  margin: 0;
  margin-top: 8px;
  color: var(--anzhiyu-secondtext);

  &:hover
    text-decoration: none !important

blockquote
  margin: 0 0 20px
  padding: 12px 15px
  border-left: 3px solid $blockquote-padding-color
  background-color: var(--blockquote-bg)
  color: var(--blockquote-color)

  footer
    cite
      &:before
        padding: 0 5px
        content: '—'

  & > :last-child
    margin-bottom: 0 !important
.time_hidden 
  display: none!important
#single_top
  padding: 1.875rem 1.5rem 0
  max-width: 1400px
  width 100%
  margin 0 auto
  & + #content-inner
    +maxWidth768()
      padding:1.875rem 0rem 0
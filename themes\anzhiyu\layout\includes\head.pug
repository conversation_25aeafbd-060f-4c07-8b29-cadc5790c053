- var pageTitle
- is_archive() ? page.title = findArchivesTitle(page, theme.menu, date) : ''
- if (is_tag()) pageTitle = _p('page.tag') + ': ' + page.tag
- else if (is_category()) pageTitle = _p('page.category') + ': ' + page.category
- else if (is_current('/404.html', [strict])) pageTitle = _p('error404')
- else pageTitle = page.title || config.title || ''

- var isSubtitle = config.subtitle ? ' - ' + config.subtitle : ''
- var tabTitle = is_home() || !pageTitle ? config.title + isSubtitle : pageTitle + ' | ' + config.title

- var pageKeywords
- if (page.keywords) pageKeywords = Array.isArray(page.keywords) ? (page.keywords).join(',') :  ([]).join(',') || page.keywords
- else if (page.tags && page.tags.length) pageKeywords = page.tags.data.map(function(tag) {return tag.name;}).join(',')
- else pageKeywords = Array.isArray(config.keywords) ? (config.keywords).join(','):  ([]).join(',') || config.keywords
- var pageAuthor = config.email ? config.author + ',' + config.email : config.author
- var pageCopyright = config.copyright || config.author
- var themeColorLight = theme.theme_color && theme.theme_color.enable && theme.theme_color.meta_theme_color_light || '#ffffff'
- var themeColorDark = theme.theme_color && theme.theme_color.enable && theme.theme_color.meta_theme_color_dark || '#0d0d0d'
- var themeColor = theme.display_mode === 'dark' ? themeColorDark : themeColorLight

meta(charset='UTF-8')
meta(http-equiv="X-UA-Compatible" content="IE=edge")
meta(name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no")
title= tabTitle
if pageKeywords
  meta(name="keywords" content=pageKeywords)
meta(name="author" content=pageAuthor)
meta(name="copyright" content=pageCopyright)
meta(name ="format-detection" content="telephone=no")
meta(name="theme-color" content=themeColor)
meta(name="mobile-web-app-capable", content="yes")
meta(name="apple-touch-fullscreen", content="yes")
meta(name="apple-mobile-web-app-title", content=pageTitle)
meta(name="application-name", content=pageTitle)
meta(name="apple-mobile-web-app-capable", content="yes")
meta(name="apple-mobile-web-app-status-bar-style", content=themeColor)


//- Open_Graph
include ./head/Open_Graph.pug

!=favicon_tag(theme.favicon || config.favicon)
link(rel="canonical" href=urlNoIndex())

//- 預解析
!=partial('includes/head/preconnect', {}, {cache: true})

//- 網站驗證
!=partial('includes/head/site_verification', {}, {cache: true})

//- PWA
if (theme.pwa && theme.pwa.enable)
  !=partial('includes/head/pwa', {}, {cache: true})


//- animation依赖
if (theme.icons && theme.icons.fontawesome_animation_css)
  link(rel="stylesheet", href=url_for(theme.icons.fontawesome_animation_css))

//- main css
link(rel='stylesheet', href=url_for(theme.asset.main_css))
if (theme.icons && theme.icons.fontawesome)
  link(rel='stylesheet', href=url_for(theme.asset.fontawesome) media="print" onload="this.media='all'")

if (theme.snackbar && theme.snackbar.enable)
  link(rel='stylesheet', href=url_for(theme.asset.snackbar_css) media="print" onload="this.media='all'")

if theme.fancybox
  link(rel='stylesheet' href=url_for(theme.asset.fancybox_css) media="print" onload="this.media='all'")

if site.data.essay || theme.home_top.swiper.enable
  link(rel='stylesheet' href=url_for(theme.home_top.swiper.swiper_css) media="print" onload="this.media='all'")

//- google_adsense
!=partial('includes/head/google_adsense', {}, {cache: true})

//- analytics
!=partial('includes/head/analytics', {}, {cache: true})

//- font
if theme.blog_title_font &&　theme.blog_title_font.font_link
  link(rel='stylesheet' href=url_for(theme.blog_title_font.font_link) media="print" onload="this.media='all'")

//- global config
!=partial('includes/head/config', {}, {cache: true})

include ./head/config_site.pug
include ./head/noscript.pug

!=fragment_cache('injectHeadJs', function(){return inject_head_js()})

!=fragment_cache('injectHead', function(){return injectHtml(theme.inject.head)})
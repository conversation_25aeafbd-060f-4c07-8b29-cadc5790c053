@echo off
chcp 65001 >nul
echo.
echo ========================================
echo    博客顶部图适配封面图脚本
echo ========================================
echo.
echo 此脚本将为每篇文章配置与封面图适配的顶部图
echo 根据文章主题选择相应风格的顶部图
echo.
echo 选择运行模式：
echo 1. 只为没有顶部图的文章添加（推荐）
echo 2. 强制更新所有文章的顶部图
echo.
set /p choice=请输入选择 (1 或 2): 

if "%choice%"=="1" (
    echo.
    echo 🚀 正在为文章配置适配封面图的顶部图...
    echo.
    python tools/sync-top-img-with-cover.py
) else if "%choice%"=="2" (
    echo.
    echo 🔄 强制更新模式：正在为所有文章重新配置顶部图...
    echo.
    python tools/sync-top-img-with-cover.py --force
) else (
    echo.
    echo ❌ 无效选择，默认使用模式1
    echo.
    python tools/sync-top-img-with-cover.py
)

echo.
echo ========================================
echo 脚本执行完成！
echo ========================================
echo.
echo 💡 提示：
echo - 顶部图已根据封面图主题进行适配
echo - 技术类文章使用深色科技风顶部图
echo - 设计类文章使用创意渐变风顶部图
echo - 生活类文章使用自然温馨风顶部图
echo - 学习类文章使用知识书香风顶部图
echo.
pause
